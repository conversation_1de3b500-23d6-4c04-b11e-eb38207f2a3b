# Portfolio Website - Nguy<PERSON>n <PERSON><PERSON><PERSON>

Một website portfolio hiện đại được xây dựng bằng React, TypeScript và Vite dành cho IT Fresher.

## 🚀 Tính năng

- **Responsive Design**: Tương thích với mọi thiết bị (desktop, tablet, mobile)
- **Modern UI/UX**: Giao diện hiện đại với animations mượt mà
- **Component-based**: Kiến trúc component tái sử dụng
- **TypeScript**: Type safety và developer experience tốt hơn
- **Performance**: Tối ưu hóa hiệu suất với Vite
- **SEO Friendly**: Meta tags và semantic HTML

## 📋 Các phần chính

1. **Header/Navigation** - Menu điều hướng sticky với active states
2. **Hero Section** - Giới thiệu với typing animation
3. **About Section** - Thông tin cá nhân và học vấn
4. **Skills Section** - <PERSON><PERSON> năng với progress bars và animations
5. **Projects Section** - Showcase các dự án với filter
6. **Experience Section** - Timeline kinh nghiệm làm việc
7. **Contact Section** - Form liên hệ và thông tin contact
8. **Footer** - Links và thông tin bổ sung

## 🛠️ Công nghệ sử dụng

- **Frontend**: React 19, TypeScript
- **Build Tool**: Vite 7
- **Styling**: CSS3 với CSS Variables
- **Icons**: SVG icons tùy chỉnh
- **Fonts**: Google Fonts (Inter)
- **Linting**: ESLint với TypeScript rules

## 📦 Cài đặt và chạy

### Yêu cầu hệ thống
- Node.js 18+
- npm hoặc yarn

### Cài đặt
```bash
# Clone repository
git clone <repository-url>
cd duc-pofolio-it-fresher

# Cài đặt dependencies
npm install

# Chạy development server
npm run dev

# Build cho production
npm run build

# Preview production build
npm run preview
```

### Scripts có sẵn
- `npm run dev` - Chạy development server
- `npm run build` - Build cho production
- `npm run preview` - Preview production build
- `npm run lint` - Chạy ESLint

## 🎨 Tùy chỉnh

### Thay đổi thông tin cá nhân
Chỉnh sửa file `src/data/portfolioData.ts`:

```typescript
export const personalInfo: PersonalInfo = {
  name: "Tên của bạn",
  title: "Chức danh",
  subtitle: "Mô tả ngắn",
  description: "Mô tả chi tiết...",
  email: "<EMAIL>",
  phone: "+84 xxx xxx xxx",
  location: "Địa chỉ",
  github: "https://github.com/username",
  linkedin: "https://linkedin.com/in/username"
};
```

### Thêm/sửa kỹ năng
```typescript
export const skills: Skill[] = [
  {
    name: "Tên kỹ năng",
    level: 85, // 0-100
    category: "frontend" // frontend, backend, database, tools, other
  }
];
```

### Thêm dự án mới
```typescript
export const projects: Project[] = [
  {
    id: "unique-id",
    title: "Tên dự án",
    description: "Mô tả dự án",
    technologies: ["React", "TypeScript"],
    features: ["Tính năng 1", "Tính năng 2"],
    githubUrl: "https://github.com/...",
    demoUrl: "https://demo-url.com"
  }
];
```

## 🎯 Tối ưu hóa

### Performance
- Lazy loading cho images
- Code splitting với React.lazy()
- CSS optimization
- Bundle size optimization

### SEO
- Semantic HTML structure
- Meta tags optimization
- Open Graph tags
- Structured data

### Accessibility
- ARIA labels
- Keyboard navigation
- Screen reader support
- Color contrast compliance

## 📱 Responsive Breakpoints

- **Desktop**: 1024px+
- **Tablet**: 768px - 1023px
- **Mobile**: 320px - 767px

## 🌟 Tính năng nổi bật

- **Smooth Scrolling**: Navigation mượt mà giữa các sections
- **Typing Animation**: Hiệu ứng typing trong Hero section
- **Progress Animations**: Skill bars với animation khi scroll
- **Interactive Elements**: Hover effects và micro-interactions
- **Form Validation**: Validation cho contact form
- **Dark Mode Ready**: Chuẩn bị sẵn cho dark mode

## 📄 Cấu trúc thư mục

```
src/
├── components/          # React components
│   ├── Header.tsx
│   ├── Hero.tsx
│   ├── About.tsx
│   ├── Skills.tsx
│   ├── Projects.tsx
│   ├── Experience.tsx
│   ├── Contact.tsx
│   ├── Footer.tsx
│   └── *.css          # Component styles
├── data/              # Static data
│   └── portfolioData.ts
├── App.tsx            # Main App component
├── App.css           # Global styles
├── index.css         # Base styles
└── main.tsx          # Entry point
```

## 🚀 Deployment

### Vercel (Recommended)
```bash
npm run build
# Deploy dist folder to Vercel
```

### Netlify
```bash
npm run build
# Deploy dist folder to Netlify
```

### GitHub Pages
```bash
npm run build
# Deploy dist folder to gh-pages branch
```

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## 📝 License

Distributed under the MIT License. See `LICENSE` for more information.

## 📞 Liên hệ

Nguyễn Văn Đức - <EMAIL>

Project Link: [https://github.com/ducnguyen/portfolio](https://github.com/ducnguyen/portfolio)
