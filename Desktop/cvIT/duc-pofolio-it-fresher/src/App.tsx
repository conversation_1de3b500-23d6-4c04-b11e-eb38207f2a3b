import React, { useState, useEffect } from 'react';
import Header from './components/Header';
import Hero from './components/Hero';
import About from './components/About';
import Skills from './components/Skills';
import Projects from './components/Projects';
import Experience from './components/Experience';
import Contact from './components/Contact';
import Footer from './components/Footer';
import './App.css';

function App() {
  const [activeSection, setActiveSection] = useState('home');

  useEffect(() => {
    const handleScroll = () => {
      const sections = ['home', 'about', 'skills', 'projects', 'experience', 'contact'];
      const scrollPosition = window.scrollY + 100;

      for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
          const offsetTop = element.offsetTop;
          const offsetHeight = element.offsetHeight;

          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveSection(section);
            break;
          }
        }
      }
    };

    // Handle floating back to top button visibility
    const handleFloatingButton = () => {
      const floatingBtn = document.querySelector('.footer__floating-top');
      if (floatingBtn) {
        if (window.scrollY > 500) {
          floatingBtn.classList.add('visible');
        } else {
          floatingBtn.classList.remove('visible');
        }
      }
    };

    const handleScrollEvents = () => {
      handleScroll();
      handleFloatingButton();
    };

    window.addEventListener('scroll', handleScrollEvents);
    return () => window.removeEventListener('scroll', handleScrollEvents);
  }, []);

  return (
    <div className="app">
      <Header activeSection={activeSection} />
      <main className="app__main">
        <Hero />
        <About />
        <Skills />
        <Projects />
        <Experience />
        <Contact />
      </main>
      <Footer />
    </div>
  );
}

export default App;
