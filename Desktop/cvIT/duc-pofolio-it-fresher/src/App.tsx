import React, { useState, useEffect } from 'react';
import './App.css';

function App() {
  const [scrolled, setScrolled] = useState(false);
  const [currentText, setCurrentText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);

  const texts = ['Frontend Developer', 'React Developer', 'TypeScript Developer', 'Web Developer'];

  // Scroll effect for header
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Typing animation effect
  useEffect(() => {
    const timeout = setTimeout(() => {
      const current = texts[currentIndex];

      if (isDeleting) {
        setCurrentText(current.substring(0, currentText.length - 1));
      } else {
        setCurrentText(current.substring(0, currentText.length + 1));
      }

      if (!isDeleting && currentText === current) {
        setTimeout(() => setIsDeleting(true), 1500);
      } else if (isDeleting && currentText === '') {
        setIsDeleting(false);
        setCurrentIndex((prevIndex) => (prevIndex + 1) % texts.length);
      }
    }, isDeleting ? 50 : 100);

    return () => clearTimeout(timeout);
  }, [currentText, currentIndex, isDeleting, texts]);

  const scrollToSection = (sectionId: string) => {
    const element = document.querySelector(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  return (
    <div className="app">
      <style>{`
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Inter', sans-serif;
          line-height: 1.6;
          color: #1e293b;
          overflow-x: hidden;
        }

        .app {
          position: relative;
        }

        /* Header Animations */
        .header {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          background: ${scrolled ? 'rgba(255, 255, 255, 0.95)' : 'rgba(255, 255, 255, 0.9)'};
          backdrop-filter: blur(10px);
          padding: 1rem 2rem;
          box-shadow: ${scrolled ? '0 4px 20px rgba(0,0,0,0.1)' : '0 2px 10px rgba(0,0,0,0.05)'};
          z-index: 1000;
          transition: all 0.3s ease;
          border-bottom: 1px solid rgba(0,0,0,0.1);
        }

        .nav {
          display: flex;
          justify-content: space-between;
          alignItems: center;
          max-width: 1200px;
          margin: 0 auto;
        }

        .logo {
          fontSize: 1.5rem;
          fontWeight: 700;
          color: #2563eb;
          cursor: pointer;
          transition: transform 0.3s ease;
        }

        .logo:hover {
          transform: scale(1.05);
        }

        .nav-list {
          display: flex;
          listStyle: none;
          gap: 2rem;
          margin: 0;
          padding: 0;
        }

        .nav-link {
          textDecoration: none;
          color: #64748b;
          fontWeight: 500;
          position: relative;
          padding: 0.5rem 0;
          transition: color 0.3s ease;
          cursor: pointer;
        }

        .nav-link:hover {
          color: #2563eb;
        }

        .nav-link::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 0;
          height: 2px;
          background: #2563eb;
          transition: width 0.3s ease;
        }

        .nav-link:hover::after {
          width: 100%;
        }

        /* Floating elements */
        .floating-circle {
          position: absolute;
          border-radius: 50%;
          background: rgba(37, 99, 235, 0.1);
          animation: float 6s ease-in-out infinite;
        }

        .floating-circle:nth-child(1) {
          width: 100px;
          height: 100px;
          top: 20%;
          right: 10%;
          animation-delay: 0s;
        }

        .floating-circle:nth-child(2) {
          width: 150px;
          height: 150px;
          bottom: 20%;
          left: 10%;
          animation-delay: 2s;
        }

        .floating-circle:nth-child(3) {
          width: 80px;
          height: 80px;
          top: 60%;
          right: 20%;
          animation-delay: 4s;
        }

        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* Typing cursor */
        .typing-cursor {
          animation: blink 1s infinite;
          color: #fbbf24;
        }

        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }

        /* Button hover effects */
        .btn {
          position: relative;
          overflow: hidden;
          transition: all 0.3s ease;
        }

        .btn::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
          transition: left 0.5s;
        }

        .btn:hover::before {
          left: 100%;
        }

        .btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        /* Card hover effects */
        .card {
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(45deg, transparent, rgba(37, 99, 235, 0.05), transparent);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        .card:hover::before {
          opacity: 1;
        }

        .card:hover {
          transform: translateY(-8px);
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        /* Progress bar animation */
        .progress-bar {
          position: relative;
          overflow: hidden;
        }

        .progress-bar::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
          animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }

        /* Scroll animations */
        .fade-in {
          opacity: 0;
          transform: translateY(30px);
          animation: fadeInUp 0.8s ease forwards;
        }

        @keyframes fadeInUp {
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
          40% { transform: translateY(-10px); }
          60% { transform: translateY(-5px); }
        }

        @keyframes scrollDot {
          0% { top: 0; opacity: 1; }
          100% { top: 20px; opacity: 0; }
        }

        /* Section animations */
        .section-enter {
          opacity: 0;
          transform: translateY(50px);
          transition: all 0.8s ease;
        }

        .section-enter.visible {
          opacity: 1;
          transform: translateY(0);
        }

        /* Responsive */
        @media (max-width: 768px) {
          .nav-list {
            display: none;
          }

          .floating-circle {
            display: none;
          }

          h1 {
            fontSize: 2.5rem !important;
          }

          .hero-title {
            fontSize: 1.4rem !important;
          }
        }
      `}</style>

      <header className="header">
        <nav className="nav">
          <div className="logo" onClick={() => scrollToSection('#home')}>
            Đức<span style={{ color: '#1e293b' }}>.</span>
          </div>
          <ul className="nav-list">
            <li><span className="nav-link" onClick={() => scrollToSection('#home')}>Trang chủ</span></li>
            <li><span className="nav-link" onClick={() => scrollToSection('#about')}>Giới thiệu</span></li>
            <li><span className="nav-link" onClick={() => scrollToSection('#skills')}>Kỹ năng</span></li>
            <li><span className="nav-link" onClick={() => scrollToSection('#projects')}>Dự án</span></li>
            <li><span className="nav-link" onClick={() => scrollToSection('#contact')}>Liên hệ</span></li>
          </ul>
        </nav>
      </header>

      <main style={{ paddingTop: '80px' }}>
        {/* Hero Section */}
        <section id="home" style={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          textAlign: 'center',
          position: 'relative',
          overflow: 'hidden'
        }}>
          {/* Floating background elements */}
          <div className="floating-circle"></div>
          <div className="floating-circle"></div>
          <div className="floating-circle"></div>

          <div style={{
            zIndex: 2,
            position: 'relative',
            animation: 'fadeInUp 1s ease'
          }}>
            <div style={{
              fontSize: '1.2rem',
              marginBottom: '1rem',
              opacity: 0.9,
              animation: 'fadeInUp 1s ease 0.2s both'
            }}>
              👋 Xin chào, tôi là
            </div>

            <h1 style={{
              fontSize: '4rem',
              marginBottom: '1rem',
              fontWeight: '700',
              background: 'linear-gradient(45deg, #fff, #e2e8f0)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              animation: 'fadeInUp 1s ease 0.4s both'
            }}>
              Nguyễn Văn Đức
            </h1>

            <div style={{
              fontSize: '1.8rem',
              marginBottom: '2rem',
              minHeight: '2.5rem',
              animation: 'fadeInUp 1s ease 0.6s both'
            }}>
              <span style={{ color: '#e2e8f0' }}>Tôi là một </span>
              <span style={{ color: '#fbbf24', fontWeight: '600' }}>
                {currentText}
                <span className="typing-cursor">|</span>
              </span>
            </div>

            <p style={{
              fontSize: '1.2rem',
              marginBottom: '3rem',
              opacity: 0.9,
              maxWidth: '600px',
              margin: '0 auto 3rem auto',
              lineHeight: '1.6',
              animation: 'fadeInUp 1s ease 0.8s both'
            }}>
              Đam mê phát triển web với React, TypeScript và các công nghệ frontend hiện đại.
              Luôn sẵn sàng học hỏi và đóng góp vào các dự án thú vị.
            </p>

            <div style={{
              display: 'flex',
              gap: '1rem',
              justifyContent: 'center',
              flexWrap: 'wrap',
              animation: 'fadeInUp 1s ease 1s both'
            }}>
              <button
                className="btn"
                onClick={() => scrollToSection('#contact')}
                style={{
                  background: '#fbbf24',
                  color: '#1e293b',
                  border: 'none',
                  padding: '1rem 2rem',
                  borderRadius: '50px',
                  fontSize: '1rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}
              >
                <span>💬</span> Liên hệ với tôi
              </button>

              <button
                className="btn"
                onClick={() => scrollToSection('#projects')}
                style={{
                  background: 'transparent',
                  color: 'white',
                  border: '2px solid rgba(255,255,255,0.3)',
                  padding: '1rem 2rem',
                  borderRadius: '50px',
                  fontSize: '1rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}
              >
                <span>🚀</span> Xem dự án
              </button>
            </div>

            {/* Social Links */}
            <div style={{
              marginTop: '3rem',
              display: 'flex',
              justifyContent: 'center',
              gap: '1rem',
              animation: 'fadeInUp 1s ease 1.2s both'
            }}>
              {[
                { icon: '📧', label: 'Email', href: 'mailto:<EMAIL>' },
                { icon: '💼', label: 'LinkedIn', href: 'https://linkedin.com/in/ducnguyen' },
                { icon: '🐙', label: 'GitHub', href: 'https://github.com/ducnguyen' }
              ].map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    width: '50px',
                    height: '50px',
                    borderRadius: '50%',
                    background: 'rgba(255,255,255,0.1)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    textDecoration: 'none',
                    fontSize: '1.2rem',
                    transition: 'all 0.3s ease',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255,255,255,0.2)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = 'rgba(255,255,255,0.2)';
                    e.currentTarget.style.transform = 'translateY(-3px) scale(1.1)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'rgba(255,255,255,0.1)';
                    e.currentTarget.style.transform = 'translateY(0) scale(1)';
                  }}
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>

          {/* Scroll indicator */}
          <div style={{
            position: 'absolute',
            bottom: '2rem',
            left: '50%',
            transform: 'translateX(-50%)',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '0.5rem',
            color: 'rgba(255,255,255,0.7)',
            cursor: 'pointer',
            animation: 'bounce 2s infinite'
          }}
          onClick={() => scrollToSection('#about')}
          >
            <span style={{ fontSize: '0.9rem', fontWeight: '500' }}>Cuộn xuống</span>
            <div style={{
              width: '2px',
              height: '30px',
              background: 'rgba(255,255,255,0.5)',
              borderRadius: '1px',
              position: 'relative'
            }}>
              <div style={{
                width: '6px',
                height: '6px',
                background: 'white',
                borderRadius: '50%',
                position: 'absolute',
                left: '-2px',
                animation: 'scrollDot 2s infinite'
              }}></div>
            </div>
          </div>
        </section>

        {/* About Section */}
        <section id="about" style={{
          padding: '5rem 2rem',
          background: '#f8fafc',
          position: 'relative'
        }}>
          <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
            <div style={{ textAlign: 'center', marginBottom: '4rem' }}>
              <h2 style={{
                fontSize: '2.5rem',
                marginBottom: '1rem',
                color: '#1e293b',
                position: 'relative'
              }}>
                Giới thiệu về tôi
                <div style={{
                  position: 'absolute',
                  bottom: '-10px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '60px',
                  height: '4px',
                  background: 'linear-gradient(45deg, #2563eb, #3b82f6)',
                  borderRadius: '2px'
                }}></div>
              </h2>
              <p style={{
                fontSize: '1.1rem',
                color: '#64748b',
                maxWidth: '600px',
                margin: '0 auto',
                lineHeight: '1.6'
              }}>
                Tìm hiểu thêm về hành trình và đam mê của tôi trong lĩnh vực công nghệ
              </p>
            </div>

            <div style={{
              display: 'grid',
              gridTemplateColumns: '2fr 1fr',
              gap: '3rem',
              alignItems: 'start'
            }}>
              {/* Main Info */}
              <div className="card" style={{
                background: 'white',
                padding: '2.5rem',
                borderRadius: '16px',
                boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
                border: '1px solid #e2e8f0'
              }}>
                <h3 style={{
                  fontSize: '1.5rem',
                  fontWeight: '600',
                  color: '#1e293b',
                  marginBottom: '1.5rem'
                }}>
                  Câu chuyện của tôi
                </h3>
                <div style={{ color: '#475569', lineHeight: '1.7', fontSize: '1rem' }}>
                  <p style={{ marginBottom: '1.2rem' }}>
                    Xin chào! Tôi là <strong style={{ color: '#2563eb' }}>Nguyễn Văn Đức</strong>, một IT Fresher đầy đam mê
                    với việc tạo ra những sản phẩm web hiện đại và thân thiện với người dùng.
                    Hành trình của tôi bắt đầu từ sự tò mò về cách thức hoạt động của các website.
                  </p>
                  <p style={{ marginBottom: '1.2rem' }}>
                    Trong quá trình học tập, tôi đã phát triển kỹ năng trong việc
                    xây dựng giao diện người dùng với <strong style={{ color: '#2563eb' }}>React</strong>, <strong style={{ color: '#2563eb' }}>TypeScript</strong>
                    và các công nghệ frontend hiện đại.
                  </p>
                  <p>
                    Mục tiêu của tôi là trở thành một Frontend Developer chuyên nghiệp,
                    đóng góp vào việc tạo ra những sản phẩm có giá trị thực sự.
                  </p>
                </div>

                {/* Contact Details */}
                <div style={{
                  marginTop: '2rem',
                  paddingTop: '2rem',
                  borderTop: '1px solid #e2e8f0'
                }}>
                  {[
                    { icon: '📧', label: 'Email:', value: '<EMAIL>', href: 'mailto:<EMAIL>' },
                    { icon: '📱', label: 'Điện thoại:', value: '+84 123 456 789', href: 'tel:+84123456789' },
                    { icon: '📍', label: 'Địa chỉ:', value: 'Hà Nội, Việt Nam', href: '#' }
                  ].map((item, index) => (
                    <div key={index} style={{
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: '1rem',
                      gap: '0.5rem'
                    }}>
                      <span style={{ fontSize: '1.2rem' }}>{item.icon}</span>
                      <span style={{ fontWeight: '500', color: '#374151', minWidth: '100px' }}>{item.label}</span>
                      {item.href.startsWith('http') || item.href.startsWith('mailto') || item.href.startsWith('tel') ? (
                        <a href={item.href} style={{ color: '#2563eb', textDecoration: 'none', fontWeight: '500' }}>
                          {item.value}
                        </a>
                      ) : (
                        <span style={{ color: '#2563eb', fontWeight: '500' }}>{item.value}</span>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Stats Cards */}
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                {[
                  { number: '2+', label: 'Năm học tập', icon: '📚' },
                  { number: '10+', label: 'Dự án hoàn thành', icon: '🚀' },
                  { number: '5+', label: 'Công nghệ thành thạo', icon: '💻' },
                  { number: '100%', label: 'Cam kết chất lượng', icon: '⭐' }
                ].map((stat, index) => (
                  <div key={index} className="card" style={{
                    background: 'white',
                    padding: '2rem 1.5rem',
                    borderRadius: '12px',
                    textAlign: 'center',
                    boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
                    border: '1px solid #e2e8f0'
                  }}>
                    <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>{stat.icon}</div>
                    <div style={{
                      fontSize: '2rem',
                      fontWeight: '700',
                      color: '#2563eb',
                      marginBottom: '0.5rem'
                    }}>
                      {stat.number}
                    </div>
                    <div style={{
                      fontSize: '0.9rem',
                      color: '#64748b',
                      fontWeight: '500'
                    }}>
                      {stat.label}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Highlights */}
            <div style={{ marginTop: '4rem' }}>
              <h3 style={{
                fontSize: '1.8rem',
                fontWeight: '600',
                color: '#1e293b',
                textAlign: 'center',
                marginBottom: '2rem'
              }}>
                Điểm mạnh của tôi
              </h3>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                gap: '2rem'
              }}>
                {[
                  { icon: '🎯', title: 'Mục tiêu rõ ràng', desc: 'Luôn đặt mục tiêu cụ thể và nỗ lực không ngừng để đạt được kết quả tốt nhất.' },
                  { icon: '🚀', title: 'Học hỏi nhanh', desc: 'Có khả năng tiếp thu và áp dụng công nghệ mới một cách nhanh chóng và hiệu quả.' },
                  { icon: '🤝', title: 'Làm việc nhóm', desc: 'Giao tiếp tốt và có khả năng làm việc hiệu quả trong môi trường nhóm.' },
                  { icon: '💡', title: 'Sáng tạo', desc: 'Luôn tìm kiếm những giải pháp sáng tạo và tối ưu cho các vấn đề phát sinh.' }
                ].map((highlight, index) => (
                  <div key={index} className="card" style={{
                    background: 'white',
                    padding: '2rem',
                    borderRadius: '12px',
                    textAlign: 'center',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                    border: '1px solid #e2e8f0'
                  }}>
                    <div style={{ fontSize: '2.5rem', marginBottom: '1rem' }}>{highlight.icon}</div>
                    <h4 style={{
                      fontSize: '1.1rem',
                      fontWeight: '600',
                      color: '#1e293b',
                      marginBottom: '0.5rem'
                    }}>
                      {highlight.title}
                    </h4>
                    <p style={{
                      fontSize: '0.9rem',
                      color: '#64748b',
                      lineHeight: '1.5',
                      margin: 0
                    }}>
                      {highlight.desc}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Skills Section */}
        <section id="skills" style={{
          padding: '5rem 2rem',
          background: 'white',
          position: 'relative'
        }}>
          <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
            <div style={{ textAlign: 'center', marginBottom: '4rem' }}>
              <h2 style={{
                fontSize: '2.5rem',
                marginBottom: '1rem',
                color: '#1e293b',
                position: 'relative'
              }}>
                Kỹ năng & Công nghệ
                <div style={{
                  position: 'absolute',
                  bottom: '-10px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '60px',
                  height: '4px',
                  background: 'linear-gradient(45deg, #2563eb, #3b82f6)',
                  borderRadius: '2px'
                }}></div>
              </h2>
              <p style={{
                fontSize: '1.1rem',
                color: '#64748b',
                maxWidth: '600px',
                margin: '0 auto',
                lineHeight: '1.6'
              }}>
                Những công nghệ và kỹ năng tôi đã học được và áp dụng trong các dự án
              </p>
            </div>

            {/* Skills Overview */}
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              gap: '3rem',
              marginBottom: '4rem',
              flexWrap: 'wrap'
            }}>
              {[
                { number: '15+', label: 'Kỹ năng', icon: '🛠️' },
                { number: '4', label: 'Lĩnh vực', icon: '📚' },
                { number: '75%', label: 'Trung bình', icon: '📊' }
              ].map((stat, index) => (
                <div key={index} className="card" style={{
                  background: '#f8fafc',
                  padding: '2rem',
                  borderRadius: '16px',
                  textAlign: 'center',
                  minWidth: '150px',
                  border: '1px solid #e2e8f0'
                }}>
                  <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>{stat.icon}</div>
                  <div style={{
                    fontSize: '2.5rem',
                    fontWeight: '700',
                    color: '#2563eb',
                    marginBottom: '0.5rem'
                  }}>
                    {stat.number}
                  </div>
                  <div style={{
                    fontSize: '1rem',
                    color: '#64748b',
                    fontWeight: '500'
                  }}>
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>

            {/* Skills by Category */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '3rem' }}>
              {[
                {
                  category: 'Frontend',
                  icon: '🎨',
                  color: '#3b82f6',
                  skills: [
                    { name: 'React', level: 85 },
                    { name: 'TypeScript', level: 80 },
                    { name: 'JavaScript', level: 90 },
                    { name: 'CSS3', level: 85 },
                    { name: 'HTML5', level: 95 },
                    { name: 'Tailwind CSS', level: 75 }
                  ]
                },
                {
                  category: 'Backend & Tools',
                  icon: '⚙️',
                  color: '#059669',
                  skills: [
                    { name: 'Node.js', level: 70 },
                    { name: 'Git', level: 80 },
                    { name: 'VS Code', level: 95 },
                    { name: 'Figma', level: 65 },
                    { name: 'MySQL', level: 60 },
                    { name: 'MongoDB', level: 55 }
                  ]
                }
              ].map((category, categoryIndex) => (
                <div key={categoryIndex} className="card" style={{
                  background: '#f8fafc',
                  borderRadius: '20px',
                  padding: '2.5rem',
                  border: '1px solid #e2e8f0'
                }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '1rem',
                    marginBottom: '2rem',
                    flexWrap: 'wrap'
                  }}>
                    <div style={{
                      fontSize: '2rem',
                      width: '60px',
                      height: '60px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      background: 'white',
                      borderRadius: '12px',
                      boxShadow: '0 4px 6px rgba(0,0,0,0.1)'
                    }}>
                      {category.icon}
                    </div>
                    <h3 style={{
                      fontSize: '1.5rem',
                      fontWeight: '600',
                      color: '#1e293b',
                      margin: 0,
                      flex: 1
                    }}>
                      {category.category}
                    </h3>
                    <div style={{
                      background: category.color,
                      color: 'white',
                      padding: '0.5rem 1rem',
                      borderRadius: '20px',
                      fontSize: '0.9rem',
                      fontWeight: '500'
                    }}>
                      {category.skills.length} kỹ năng
                    </div>
                  </div>

                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                    gap: '1.5rem'
                  }}>
                    {category.skills.map((skill, skillIndex) => (
                      <div key={skillIndex} className="card" style={{
                        background: 'white',
                        padding: '1.5rem',
                        borderRadius: '12px',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                        border: '1px solid #e2e8f0'
                      }}>
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          marginBottom: '1rem'
                        }}>
                          <h4 style={{
                            fontSize: '1.1rem',
                            fontWeight: '600',
                            color: '#1e293b',
                            margin: 0
                          }}>
                            {skill.name}
                          </h4>
                          <div style={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'flex-end',
                            gap: '0.25rem'
                          }}>
                            <span style={{
                              background: skill.level >= 80 ? '#059669' : skill.level >= 60 ? '#3b82f6' : '#f59e0b',
                              color: 'white',
                              padding: '0.25rem 0.75rem',
                              borderRadius: '12px',
                              fontSize: '0.75rem',
                              fontWeight: '600',
                              textTransform: 'uppercase'
                            }}>
                              {skill.level >= 80 ? 'Expert' : skill.level >= 60 ? 'Advanced' : 'Intermediate'}
                            </span>
                            <span style={{
                              fontSize: '0.9rem',
                              fontWeight: '600',
                              color: '#64748b'
                            }}>
                              {skill.level}%
                            </span>
                          </div>
                        </div>

                        <div className="progress-bar" style={{
                          width: '100%',
                          height: '8px',
                          background: '#e2e8f0',
                          borderRadius: '4px',
                          overflow: 'hidden'
                        }}>
                          <div style={{
                            height: '100%',
                            width: `${skill.level}%`,
                            background: category.color,
                            borderRadius: '4px',
                            transition: 'width 1.5s ease-out'
                          }}></div>
                        </div>

                        <div style={{
                          display: 'flex',
                          justifyContent: 'center',
                          marginTop: '1rem'
                        }}>
                          <div style={{ fontSize: '1.2rem' }}>
                            {skill.level >= 80 && '⭐⭐⭐'}
                            {skill.level >= 60 && skill.level < 80 && '⭐⭐'}
                            {skill.level >= 40 && skill.level < 60 && '⭐'}
                            {skill.level < 40 && '📚'}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* Learning Path */}
            <div style={{
              marginTop: '4rem',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              padding: '3rem',
              borderRadius: '20px',
              color: 'white'
            }}>
              <h3 style={{
                fontSize: '1.8rem',
                fontWeight: '600',
                textAlign: 'center',
                marginBottom: '2rem'
              }}>
                Đang học và phát triển
              </h3>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: '2rem'
              }}>
                {[
                  { icon: '🎯', title: 'Next.js & SSR', desc: 'Đang tìm hiểu về Server-Side Rendering và static generation' },
                  { icon: '🔧', title: 'Testing', desc: 'Học cách viết unit test và integration test cho React apps' },
                  { icon: '☁️', title: 'AWS & Cloud', desc: 'Tìm hiểu về deployment và quản lý ứng dụng trên cloud' }
                ].map((item, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    gap: '1rem',
                    background: 'rgba(255, 255, 255, 0.1)',
                    padding: '1.5rem',
                    borderRadius: '12px',
                    backdropFilter: 'blur(10px)'
                  }}>
                    <div style={{ fontSize: '2rem', flexShrink: 0 }}>{item.icon}</div>
                    <div>
                      <h4 style={{
                        fontSize: '1.1rem',
                        fontWeight: '600',
                        marginBottom: '0.5rem'
                      }}>
                        {item.title}
                      </h4>
                      <p style={{
                        margin: 0,
                        opacity: 0.9,
                        lineHeight: '1.5',
                        fontSize: '0.9rem'
                      }}>
                        {item.desc}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Projects Section */}
        <section id="projects" style={{
          padding: '5rem 2rem',
          background: '#f8fafc',
          position: 'relative'
        }}>
          <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
            <div style={{ textAlign: 'center', marginBottom: '4rem' }}>
              <h2 style={{
                fontSize: '2.5rem',
                marginBottom: '1rem',
                color: '#1e293b',
                position: 'relative'
              }}>
                Dự án của tôi
                <div style={{
                  position: 'absolute',
                  bottom: '-10px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '60px',
                  height: '4px',
                  background: 'linear-gradient(45deg, #2563eb, #3b82f6)',
                  borderRadius: '2px'
                }}></div>
              </h2>
              <p style={{
                fontSize: '1.1rem',
                color: '#64748b',
                maxWidth: '600px',
                margin: '0 auto',
                lineHeight: '1.6'
              }}>
                Những dự án tôi đã thực hiện trong quá trình học tập và phát triển kỹ năng
              </p>
            </div>

            {/* Filter Buttons */}
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              gap: '1rem',
              marginBottom: '3rem',
              flexWrap: 'wrap'
            }}>
              {['Tất cả (3)', 'React (3)', 'TypeScript (2)', 'Node.js (1)'].map((filter, index) => (
                <button key={index} style={{
                  padding: '0.75rem 1.5rem',
                  border: index === 0 ? '2px solid #2563eb' : '2px solid #e2e8f0',
                  background: index === 0 ? '#2563eb' : 'white',
                  color: index === 0 ? 'white' : '#64748b',
                  borderRadius: '50px',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  fontSize: '0.9rem'
                }}
                onMouseEnter={(e) => {
                  if (index !== 0) {
                    e.currentTarget.style.borderColor = '#2563eb';
                    e.currentTarget.style.color = '#2563eb';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (index !== 0) {
                    e.currentTarget.style.borderColor = '#e2e8f0';
                    e.currentTarget.style.color = '#64748b';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }
                }}
                >
                  {filter}
                </button>
              ))}
            </div>

            {/* Projects Grid */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
              gap: '2rem',
              marginBottom: '4rem'
            }}>
              {[
                {
                  title: 'E-commerce Website',
                  desc: 'Website thương mại điện tử hoàn chỉnh với giỏ hàng, thanh toán và quản lý sản phẩm',
                  tech: ['React', 'TypeScript', 'Node.js', 'MongoDB'],
                  features: ['Đăng nhập/đăng ký', 'Giỏ hàng', 'Thanh toán', 'Admin dashboard'],
                  gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                },
                {
                  title: 'Task Management App',
                  desc: 'Ứng dụng quản lý công việc với drag & drop, deadline và thông báo',
                  tech: ['React', 'TypeScript', 'Firebase'],
                  features: ['Drag & drop', 'Real-time updates', 'Team collaboration', 'Mobile responsive'],
                  gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
                },
                {
                  title: 'Weather App',
                  desc: 'Ứng dụng dự báo thời tiết với giao diện đẹp và dữ liệu real-time',
                  tech: ['React', 'JavaScript', 'OpenWeather API'],
                  features: ['Current weather', '7-day forecast', 'Location search', 'Dark/Light theme'],
                  gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
                }
              ].map((project, index) => (
                <div key={index} className="card" style={{
                  background: 'white',
                  borderRadius: '16px',
                  overflow: 'hidden',
                  boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
                  border: '1px solid #e2e8f0',
                  position: 'relative'
                }}>
                  {/* Project Image/Placeholder */}
                  <div style={{
                    height: '200px',
                    background: project.gradient,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    position: 'relative',
                    overflow: 'hidden'
                  }}>
                    <div style={{
                      textAlign: 'center',
                      zIndex: 2
                    }}>
                      <div style={{ fontSize: '3rem', marginBottom: '0.5rem' }}>
                        {index === 0 ? '🛒' : index === 1 ? '📋' : '🌤️'}
                      </div>
                      <span style={{ fontSize: '1.1rem', fontWeight: '500' }}>Demo Project</span>
                    </div>
                    {/* Floating elements */}
                    <div style={{
                      position: 'absolute',
                      top: '20%',
                      right: '10%',
                      width: '60px',
                      height: '60px',
                      borderRadius: '50%',
                      background: 'rgba(255,255,255,0.1)',
                      animation: 'float 3s ease-in-out infinite'
                    }}></div>
                    <div style={{
                      position: 'absolute',
                      bottom: '20%',
                      left: '15%',
                      width: '40px',
                      height: '40px',
                      borderRadius: '50%',
                      background: 'rgba(255,255,255,0.1)',
                      animation: 'float 3s ease-in-out infinite',
                      animationDelay: '1s'
                    }}></div>
                  </div>

                  {/* Project Content */}
                  <div style={{ padding: '2rem' }}>
                    <h3 style={{
                      fontSize: '1.25rem',
                      fontWeight: '600',
                      color: '#1e293b',
                      marginBottom: '0.75rem'
                    }}>
                      {project.title}
                    </h3>
                    <p style={{
                      color: '#64748b',
                      lineHeight: '1.6',
                      marginBottom: '1.5rem',
                      fontSize: '0.95rem'
                    }}>
                      {project.desc}
                    </p>

                    {/* Technologies */}
                    <div style={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      gap: '0.5rem',
                      marginBottom: '1.5rem'
                    }}>
                      {project.tech.map((tech, techIndex) => (
                        <span key={techIndex} style={{
                          background: '#f1f5f9',
                          color: '#475569',
                          padding: '0.25rem 0.75rem',
                          borderRadius: '12px',
                          fontSize: '0.8rem',
                          fontWeight: '500',
                          border: '1px solid #e2e8f0'
                        }}>
                          {tech}
                        </span>
                      ))}
                    </div>

                    {/* Features */}
                    <div style={{ marginBottom: '1.5rem' }}>
                      <h4 style={{
                        fontSize: '0.9rem',
                        fontWeight: '600',
                        color: '#374151',
                        marginBottom: '0.75rem'
                      }}>
                        Tính năng chính:
                      </h4>
                      <ul style={{
                        listStyle: 'none',
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '0.5rem'
                      }}>
                        {project.features.slice(0, 3).map((feature, featureIndex) => (
                          <li key={featureIndex} style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem',
                            color: '#64748b',
                            fontSize: '0.85rem'
                          }}>
                            <span style={{ color: '#059669', fontSize: '0.8rem' }}>✓</span>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Project Links */}
                    <div style={{
                      display: 'flex',
                      gap: '1rem',
                      marginBottom: '1rem'
                    }}>
                      <button className="btn" style={{
                        background: '#2563eb',
                        color: 'white',
                        border: 'none',
                        padding: '0.5rem 1rem',
                        borderRadius: '8px',
                        fontSize: '0.9rem',
                        fontWeight: '500',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        flex: 1,
                        justifyContent: 'center'
                      }}>
                        <span>🚀</span> Live Demo
                      </button>
                      <button className="btn" style={{
                        background: '#374151',
                        color: 'white',
                        border: 'none',
                        padding: '0.5rem 1rem',
                        borderRadius: '8px',
                        fontSize: '0.9rem',
                        fontWeight: '500',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        flex: 1,
                        justifyContent: 'center'
                      }}>
                        <span>📂</span> GitHub
                      </button>
                    </div>

                    {/* Expand Button */}
                    <button style={{
                      width: '100%',
                      background: 'none',
                      border: '1px solid #e2e8f0',
                      color: '#2563eb',
                      padding: '0.75rem',
                      borderRadius: '8px',
                      cursor: 'pointer',
                      fontWeight: '500',
                      fontSize: '0.9rem',
                      transition: 'all 0.3s ease',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '0.5rem'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = '#f8fafc';
                      e.currentTarget.style.borderColor = '#2563eb';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = 'none';
                      e.currentTarget.style.borderColor = '#e2e8f0';
                    }}
                    >
                      <span>👁️</span> Xem chi tiết
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Call to Action */}
            <div style={{
              textAlign: 'center',
              padding: '3rem 2rem',
              background: 'linear-gradient(135deg, #2563eb, #3b82f6)',
              borderRadius: '16px',
              color: 'white'
            }}>
              <h3 style={{
                fontSize: '1.5rem',
                fontWeight: '600',
                marginBottom: '1rem'
              }}>
                Có ý tưởng dự án thú vị?
              </h3>
              <p style={{
                fontSize: '1.1rem',
                marginBottom: '2rem',
                opacity: 0.9,
                maxWidth: '600px',
                margin: '0 auto 2rem auto'
              }}>
                Tôi luôn sẵn sàng tham gia vào những dự án mới và thú vị.
                Hãy liên hệ để thảo luận!
              </p>
              <button
                className="btn"
                onClick={() => scrollToSection('#contact')}
                style={{
                  background: 'white',
                  color: '#2563eb',
                  border: 'none',
                  padding: '0.75rem 2rem',
                  borderRadius: '50px',
                  fontWeight: '600',
                  fontSize: '1rem',
                  cursor: 'pointer'
                }}
              >
                Liên hệ với tôi
              </button>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section id="contact" style={{
          padding: '5rem 2rem',
          background: '#f8fafc',
          position: 'relative'
        }}>
          <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
            <div style={{ textAlign: 'center', marginBottom: '4rem' }}>
              <h2 style={{
                fontSize: '2.5rem',
                marginBottom: '1rem',
                color: '#1e293b',
                position: 'relative'
              }}>
                Liên hệ với tôi
                <div style={{
                  position: 'absolute',
                  bottom: '-10px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '60px',
                  height: '4px',
                  background: 'linear-gradient(45deg, #2563eb, #3b82f6)',
                  borderRadius: '2px'
                }}></div>
              </h2>
              <p style={{
                fontSize: '1.1rem',
                color: '#64748b',
                maxWidth: '600px',
                margin: '0 auto',
                lineHeight: '1.6'
              }}>
                Sẵn sàng thảo luận về cơ hội hợp tác hoặc dự án mới. Hãy liên hệ!
              </p>
            </div>

            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '4rem',
              marginBottom: '4rem'
            }}>
              {/* Contact Info */}
              <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
                <div className="card" style={{
                  background: 'white',
                  padding: '2rem',
                  borderRadius: '16px',
                  boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
                  border: '1px solid #e2e8f0'
                }}>
                  <h3 style={{
                    fontSize: '1.5rem',
                    fontWeight: '600',
                    color: '#1e293b',
                    marginBottom: '1rem'
                  }}>
                    Hãy kết nối với tôi!
                  </h3>
                  <p style={{
                    color: '#64748b',
                    lineHeight: '1.6',
                    marginBottom: '2rem'
                  }}>
                    Tôi luôn sẵn sàng lắng nghe những ý tưởng mới và cơ hội hợp tác thú vị.
                    Dù bạn có một dự án cần thực hiện hay chỉ muốn trò chuyện về công nghệ.
                  </p>

                  {/* Contact Methods */}
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                    {[
                      { icon: '📧', title: 'Email', value: '<EMAIL>', href: 'mailto:<EMAIL>', desc: 'Gửi email trực tiếp' },
                      { icon: '📱', title: 'Điện thoại', value: '+84 123 456 789', href: 'tel:+84123456789', desc: 'Gọi điện trực tiếp' },
                      { icon: '📍', title: 'Địa chỉ', value: 'Hà Nội, Việt Nam', href: '#', desc: 'Vị trí hiện tại' }
                    ].map((method, index) => (
                      <a key={index} href={method.href} className="card" style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '1rem',
                        background: '#f8fafc',
                        padding: '1.5rem',
                        borderRadius: '12px',
                        border: '1px solid #e2e8f0',
                        textDecoration: 'none',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.borderColor = '#2563eb';
                        e.currentTarget.style.background = 'white';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.borderColor = '#e2e8f0';
                        e.currentTarget.style.background = '#f8fafc';
                      }}
                      >
                        <div style={{
                          fontSize: '2rem',
                          width: '60px',
                          height: '60px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          background: 'white',
                          borderRadius: '12px',
                          flexShrink: 0,
                          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                        }}>
                          {method.icon}
                        </div>
                        <div style={{ flex: 1 }}>
                          <h4 style={{
                            fontSize: '1.1rem',
                            fontWeight: '600',
                            color: '#1e293b',
                            marginBottom: '0.25rem'
                          }}>
                            {method.title}
                          </h4>
                          <p style={{
                            color: '#2563eb',
                            fontWeight: '500',
                            marginBottom: '0.25rem'
                          }}>
                            {method.value}
                          </p>
                          <span style={{
                            color: '#64748b',
                            fontSize: '0.9rem'
                          }}>
                            {method.desc}
                          </span>
                        </div>
                      </a>
                    ))}
                  </div>
                </div>

                {/* Social Links */}
                <div className="card" style={{
                  background: 'white',
                  padding: '2rem',
                  borderRadius: '16px',
                  boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
                  border: '1px solid #e2e8f0'
                }}>
                  <h4 style={{
                    fontSize: '1.1rem',
                    fontWeight: '600',
                    color: '#1e293b',
                    marginBottom: '1rem'
                  }}>
                    Theo dõi tôi trên:
                  </h4>
                  <div style={{ display: 'flex', gap: '1rem' }}>
                    {[
                      { name: 'GitHub', url: 'https://github.com/ducnguyen', icon: '🐙' },
                      { name: 'LinkedIn', url: 'https://linkedin.com/in/ducnguyen', icon: '💼' },
                      { name: 'Facebook', url: 'https://facebook.com/ducnguyen', icon: '📘' }
                    ].map((social, index) => (
                      <a key={index} href={social.url} target="_blank" rel="noopener noreferrer" style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        padding: '0.75rem 1rem',
                        background: '#f8fafc',
                        border: '1px solid #e2e8f0',
                        borderRadius: '8px',
                        color: '#64748b',
                        textDecoration: 'none',
                        transition: 'all 0.3s ease',
                        fontWeight: '500',
                        flex: 1,
                        justifyContent: 'center'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.background = '#2563eb';
                        e.currentTarget.style.color = 'white';
                        e.currentTarget.style.borderColor = '#2563eb';
                        e.currentTarget.style.transform = 'translateY(-2px)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.background = '#f8fafc';
                        e.currentTarget.style.color = '#64748b';
                        e.currentTarget.style.borderColor = '#e2e8f0';
                        e.currentTarget.style.transform = 'translateY(0)';
                      }}
                      >
                        <span>{social.icon}</span>
                        <span>{social.name}</span>
                      </a>
                    ))}
                  </div>
                </div>
              </div>

              {/* Contact Form */}
              <div className="card" style={{
                background: 'white',
                padding: '2.5rem',
                borderRadius: '16px',
                boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
                border: '1px solid #e2e8f0',
                height: 'fit-content'
              }}>
                <h3 style={{
                  fontSize: '1.5rem',
                  fontWeight: '600',
                  color: '#1e293b',
                  marginBottom: '2rem',
                  textAlign: 'center'
                }}>
                  Gửi tin nhắn
                </h3>

                <form style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                  <div>
                    <label style={{
                      display: 'block',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '0.5rem',
                      fontSize: '0.9rem'
                    }}>
                      Họ và tên *
                    </label>
                    <input type="text" placeholder="Nhập họ và tên của bạn" style={{
                      width: '100%',
                      padding: '0.75rem 1rem',
                      border: '1px solid #d1d5db',
                      borderRadius: '8px',
                      fontSize: '1rem',
                      transition: 'all 0.3s ease'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#2563eb';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(37, 99, 235, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                    />
                  </div>

                  <div>
                    <label style={{
                      display: 'block',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '0.5rem',
                      fontSize: '0.9rem'
                    }}>
                      Email *
                    </label>
                    <input type="email" placeholder="<EMAIL>" style={{
                      width: '100%',
                      padding: '0.75rem 1rem',
                      border: '1px solid #d1d5db',
                      borderRadius: '8px',
                      fontSize: '1rem',
                      transition: 'all 0.3s ease'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#2563eb';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(37, 99, 235, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                    />
                  </div>

                  <div>
                    <label style={{
                      display: 'block',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '0.5rem',
                      fontSize: '0.9rem'
                    }}>
                      Chủ đề *
                    </label>
                    <select style={{
                      width: '100%',
                      padding: '0.75rem 1rem',
                      border: '1px solid #d1d5db',
                      borderRadius: '8px',
                      fontSize: '1rem',
                      transition: 'all 0.3s ease'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#2563eb';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(37, 99, 235, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                    >
                      <option value="">Chọn chủ đề</option>
                      <option value="job">Cơ hội việc làm</option>
                      <option value="project">Hợp tác dự án</option>
                      <option value="freelance">Freelance</option>
                      <option value="other">Khác</option>
                    </select>
                  </div>

                  <div>
                    <label style={{
                      display: 'block',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '0.5rem',
                      fontSize: '0.9rem'
                    }}>
                      Tin nhắn *
                    </label>
                    <textarea rows={6} placeholder="Hãy chia sẻ chi tiết về dự án hoặc cơ hội bạn muốn thảo luận..." style={{
                      width: '100%',
                      padding: '0.75rem 1rem',
                      border: '1px solid #d1d5db',
                      borderRadius: '8px',
                      fontSize: '1rem',
                      resize: 'vertical',
                      minHeight: '120px',
                      transition: 'all 0.3s ease'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = '#2563eb';
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(37, 99, 235, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = '#d1d5db';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                    />
                  </div>

                  <button type="submit" className="btn" style={{
                    width: '100%',
                    background: '#2563eb',
                    color: 'white',
                    border: 'none',
                    padding: '0.875rem 2rem',
                    borderRadius: '8px',
                    fontWeight: '600',
                    fontSize: '1rem',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '0.5rem'
                  }}>
                    <span>📤</span> Gửi tin nhắn
                  </button>
                </form>
              </div>
            </div>

            {/* Quick Actions */}
            <div style={{
              textAlign: 'center',
              padding: '3rem 2rem',
              background: 'linear-gradient(135deg, #2563eb, #3b82f6)',
              borderRadius: '16px',
              color: 'white'
            }}>
              <h3 style={{
                fontSize: '1.5rem',
                fontWeight: '600',
                marginBottom: '2rem'
              }}>
                Hoặc liên hệ nhanh qua:
              </h3>
              <div style={{
                display: 'flex',
                gap: '1rem',
                justifyContent: 'center',
                flexWrap: 'wrap'
              }}>
                <a href="mailto:<EMAIL>?subject=Cơ hội hợp tác" className="btn" style={{
                  background: 'white',
                  color: '#2563eb',
                  padding: '0.75rem 2rem',
                  borderRadius: '50px',
                  textDecoration: 'none',
                  fontWeight: '600',
                  fontSize: '1rem',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}>
                  <span>📧</span> Gửi Email
                </a>
                <a href="tel:+84123456789" className="btn" style={{
                  background: 'transparent',
                  color: 'white',
                  border: '2px solid rgba(255,255,255,0.3)',
                  padding: '0.75rem 2rem',
                  borderRadius: '50px',
                  textDecoration: 'none',
                  fontWeight: '600',
                  fontSize: '1rem',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}>
                  <span>📞</span> Gọi điện
                </a>
              </div>
            </div>
          </div>
        </section>
      </main>

      <footer style={{
        background: '#0f172a',
        color: '#e2e8f0'
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 2rem' }}>
          {/* Main Footer Content */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '2fr 1fr 1fr 1.5fr',
            gap: '3rem',
            padding: '4rem 0 2rem 0'
          }}>
            {/* About Section */}
            <div style={{ maxWidth: '350px' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                marginBottom: '1rem',
                fontSize: '1.5rem',
                fontWeight: '700'
              }}>
                <span style={{ color: '#f1f5f9' }}>Đức</span>
                <span style={{ color: '#2563eb', marginLeft: '2px' }}>.</span>
              </div>
              <p style={{
                color: '#94a3b8',
                lineHeight: '1.6',
                marginBottom: '1.5rem',
                fontSize: '0.95rem'
              }}>
                IT Fresher đam mê phát triển web với React và TypeScript.
                Luôn sẵn sàng học hỏi và đóng góp vào các dự án thú vị.
              </p>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  color: '#94a3b8',
                  fontSize: '0.9rem'
                }}>
                  <span style={{ color: '#2563eb' }}>📧</span>
                  <a href="mailto:<EMAIL>" style={{
                    color: '#94a3b8',
                    textDecoration: 'none',
                    transition: 'color 0.3s ease'
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.color = '#2563eb'}
                  onMouseLeave={(e) => e.currentTarget.style.color = '#94a3b8'}
                  >
                    <EMAIL>
                  </a>
                </div>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  color: '#94a3b8',
                  fontSize: '0.9rem'
                }}>
                  <span style={{ color: '#2563eb' }}>📍</span>
                  <span>Hà Nội, Việt Nam</span>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h3 style={{
                fontSize: '1.2rem',
                fontWeight: '600',
                color: '#f1f5f9',
                marginBottom: '1.5rem',
                position: 'relative'
              }}>
                Liên kết nhanh
                <div style={{
                  position: 'absolute',
                  bottom: '-8px',
                  left: 0,
                  width: '30px',
                  height: '2px',
                  background: '#2563eb'
                }}></div>
              </h3>
              <ul style={{
                listStyle: 'none',
                display: 'flex',
                flexDirection: 'column',
                gap: '0.75rem'
              }}>
                {['Trang chủ', 'Giới thiệu', 'Kỹ năng', 'Dự án', 'Liên hệ'].map((link, index) => (
                  <li key={index} style={{ transition: 'transform 0.3s ease' }}
                  onMouseEnter={(e) => e.currentTarget.style.transform = 'translateX(5px)'}
                  onMouseLeave={(e) => e.currentTarget.style.transform = 'translateX(0)'}
                  >
                    <span style={{
                      color: '#94a3b8',
                      textDecoration: 'none',
                      fontSize: '0.9rem',
                      transition: 'color 0.3s ease',
                      cursor: 'pointer'
                    }}
                    onMouseEnter={(e) => e.currentTarget.style.color = '#2563eb'}
                    onMouseLeave={(e) => e.currentTarget.style.color = '#94a3b8'}
                    onClick={() => scrollToSection(`#${link === 'Trang chủ' ? 'home' : link === 'Giới thiệu' ? 'about' : link === 'Kỹ năng' ? 'skills' : link === 'Dự án' ? 'projects' : 'contact'}`)}
                    >
                      {link}
                    </span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Services */}
            <div>
              <h3 style={{
                fontSize: '1.2rem',
                fontWeight: '600',
                color: '#f1f5f9',
                marginBottom: '1.5rem',
                position: 'relative'
              }}>
                Dịch vụ
                <div style={{
                  position: 'absolute',
                  bottom: '-8px',
                  left: 0,
                  width: '30px',
                  height: '2px',
                  background: '#2563eb'
                }}></div>
              </h3>
              <ul style={{
                listStyle: 'none',
                display: 'flex',
                flexDirection: 'column',
                gap: '0.75rem'
              }}>
                {['Frontend Development', 'React Applications', 'Responsive Design', 'Website Optimization', 'Code Review'].map((service, index) => (
                  <li key={index}>
                    <span style={{
                      color: '#94a3b8',
                      fontSize: '0.9rem'
                    }}>
                      {service}
                    </span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Social & Newsletter */}
            <div>
              <h3 style={{
                fontSize: '1.2rem',
                fontWeight: '600',
                color: '#f1f5f9',
                marginBottom: '1.5rem',
                position: 'relative'
              }}>
                Kết nối với tôi
                <div style={{
                  position: 'absolute',
                  bottom: '-8px',
                  left: 0,
                  width: '30px',
                  height: '2px',
                  background: '#2563eb'
                }}></div>
              </h3>
              <div style={{ display: 'flex', gap: '1rem', marginBottom: '2rem' }}>
                {[
                  { name: 'GitHub', url: 'https://github.com/ducnguyen', icon: '🐙' },
                  { name: 'LinkedIn', url: 'https://linkedin.com/in/ducnguyen', icon: '💼' },
                  { name: 'Facebook', url: 'https://facebook.com/ducnguyen', icon: '📘' }
                ].map((social, index) => (
                  <a key={index} href={social.url} target="_blank" rel="noopener noreferrer" style={{
                    width: '40px',
                    height: '40px',
                    borderRadius: '8px',
                    background: '#1e293b',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#94a3b8',
                    textDecoration: 'none',
                    transition: 'all 0.3s ease',
                    border: '1px solid #334155',
                    fontSize: '1.2rem'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = '#2563eb';
                    e.currentTarget.style.color = 'white';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.borderColor = '#2563eb';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = '#1e293b';
                    e.currentTarget.style.color = '#94a3b8';
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.borderColor = '#334155';
                  }}
                  >
                    {social.icon}
                  </a>
                ))}
              </div>

              <div style={{
                background: '#1e293b',
                padding: '1.5rem',
                borderRadius: '12px',
                border: '1px solid #334155'
              }}>
                <p style={{
                  color: '#94a3b8',
                  fontSize: '0.9rem',
                  marginBottom: '1rem',
                  lineHeight: '1.5'
                }}>
                  Muốn cập nhật về các dự án mới của tôi?
                </p>
                <button
                  onClick={() => scrollToSection('#contact')}
                  style={{
                    width: '100%',
                    background: '#2563eb',
                    color: 'white',
                    border: 'none',
                    padding: '0.75rem 1rem',
                    borderRadius: '8px',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    fontSize: '0.9rem'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = '#1d4ed8';
                    e.currentTarget.style.transform = 'translateY(-1px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = '#2563eb';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}
                >
                  Liên hệ ngay
                </button>
              </div>
            </div>
          </div>

          {/* Footer Bottom */}
          <div style={{
            borderTop: '1px solid #334155',
            padding: '2rem 0'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: '1rem'
            }}>
              <div style={{ color: '#64748b', fontSize: '0.9rem' }}>
                <p style={{ margin: 0, marginBottom: '0.25rem' }}>
                  © 2024 Nguyễn Văn Đức. Tất cả quyền được bảo lưu.
                </p>
                <p style={{
                  fontSize: '0.8rem',
                  color: '#475569',
                  margin: 0
                }}>
                  Được xây dựng với ❤️ bằng React & TypeScript
                </p>
              </div>

              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '2rem',
                flexWrap: 'wrap'
              }}>
                <span style={{
                  color: '#64748b',
                  fontSize: '0.9rem',
                  cursor: 'pointer',
                  transition: 'color 0.3s ease'
                }}
                onMouseEnter={(e) => e.currentTarget.style.color = '#2563eb'}
                onMouseLeave={(e) => e.currentTarget.style.color = '#64748b'}
                >
                  Privacy Policy
                </span>
                <span style={{
                  color: '#64748b',
                  fontSize: '0.9rem',
                  cursor: 'pointer',
                  transition: 'color 0.3s ease'
                }}
                onMouseEnter={(e) => e.currentTarget.style.color = '#2563eb'}
                onMouseLeave={(e) => e.currentTarget.style.color = '#64748b'}
                >
                  Terms of Service
                </span>
                <button
                  onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    background: 'none',
                    border: '1px solid #334155',
                    color: '#94a3b8',
                    padding: '0.5rem 1rem',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    fontSize: '0.9rem'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = '#1e293b';
                    e.currentTarget.style.borderColor = '#2563eb';
                    e.currentTarget.style.color = '#2563eb';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'none';
                    e.currentTarget.style.borderColor = '#334155';
                    e.currentTarget.style.color = '#94a3b8';
                  }}
                >
                  <span>⬆️</span> Lên đầu trang
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Floating Back to Top Button */}
        <button
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          style={{
            position: 'fixed',
            bottom: '2rem',
            right: '2rem',
            width: '50px',
            height: '50px',
            background: '#2563eb',
            color: 'white',
            border: 'none',
            borderRadius: '50%',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0 4px 12px rgba(37, 99, 235, 0.3)',
            transition: 'all 0.3s ease',
            zIndex: 1000,
            fontSize: '1.2rem'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = '#1d4ed8';
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 8px 25px rgba(37, 99, 235, 0.4)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = '#2563eb';
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(37, 99, 235, 0.3)';
          }}
        >
          ⬆️
        </button>
      </footer>
    </div>
  );
}

export default App;
