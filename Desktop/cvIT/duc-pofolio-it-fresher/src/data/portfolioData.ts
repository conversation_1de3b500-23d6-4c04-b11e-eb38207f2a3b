// Portfolio Data - <PERSON><PERSON> liệu cho website portfolio
export interface PersonalInfo {
  name: string;
  title: string;
  subtitle: string;
  description: string;
  email: string;
  phone: string;
  location: string;
  github: string;
  linkedin: string;
  facebook?: string;
}

export interface Skill {
  name: string;
  level: number; // 1-100
  category: 'frontend' | 'backend' | 'database' | 'tools' | 'other';
}

export interface Project {
  id: string;
  title: string;
  description: string;
  technologies: string[];
  image?: string;
  demoUrl?: string;
  githubUrl?: string;
  features: string[];
}

export interface Experience {
  id: string;
  company: string;
  position: string;
  duration: string;
  description: string[];
  technologies: string[];
}

export interface Education {
  id: string;
  school: string;
  degree: string;
  duration: string;
  gpa?: string;
  description?: string;
}

// Dữ liệu mẫu - bạn có thể thay đổi theo thông tin của mình
export const personalInfo: PersonalInfo = {
  name: "<PERSON><PERSON><PERSON> Đ<PERSON>",
  title: "Fresher Java",
  subtitle: "Frontend Developer",
  description: "<PERSON><PERSON><PERSON> là một Fresher Java đam mê phát triển web với kinh nghiệm về <PERSON> Springboot, React và các công nghệ frontend hiện đại. Luôn sẵn sàng học hỏi và đóng góp vào các dự án thú vị.",
  email: "<EMAIL>",
  phone: "+84 ***********",
  location: "207A Nguyễn Phúc Chu P15 Tân Bình, Tp Hồ Ch<PERSON>, Việt Nam",
  github: "https://github.com/duc15112003",
  linkedin: "https://linkedin.com/in/ducnguyen",
  facebook: "https://facebook.com/ducnguyen"
};

export const skills: Skill[] = [
  // Frontend
  { name: "Java", level: 85, category: "backend" },
  { name: "SpringBoot Framework", level: 80, category: "backend" },
  { name: "React", level: 80, category: "frontend" },
  { name: "Tailwind CSS", level: 70, category: "frontend" },
  { name: "Bootstrap", level: 75, category: "frontend" },

  // Database
  { name: "MySQL", level: 65, category: "database" },
  { name: "MongoDB", level: 60, category: "database" },
  { name: "PostgreSQL", level: 45, category: "database" },
  
  // Tools
  { name: "Git", level: 75, category: "tools" },
  { name: "Docker", level: 60, category: "tools" },
  { name: "Figma", level: 60, category: "tools" },
];

export const projects: Project[] = [
  {
    id: "1",
    title: "E-commerce Website",
    description: "Website thương mại điện tử hoàn chỉnh với giỏ hàng, thanh toán và quản lý sản phẩm",
    technologies: ["React", "TypeScript", "Node.js", "MongoDB", "Express"],
    features: [
      "Đăng nhập/đăng ký người dùng",
      "Giỏ hàng và thanh toán",
      "Quản lý sản phẩm",
      "Responsive design",
      "Admin dashboard"
    ],
    githubUrl: "https://github.com/ducnguyen/ecommerce-project",
    demoUrl: "https://ecommerce-demo.vercel.app"
  },
  {
    id: "2",
    title: "Task Management App",
    description: "Ứng dụng quản lý công việc với drag & drop, deadline và thông báo",
    technologies: ["React", "TypeScript", "Firebase", "Material-UI"],
    features: [
      "Drag & drop tasks",
      "Deadline tracking",
      "Team collaboration",
      "Real-time updates",
      "Mobile responsive"
    ],
    githubUrl: "https://github.com/ducnguyen/task-manager",
    demoUrl: "https://task-manager-demo.vercel.app"
  },
  {
    id: "3",
    title: "Weather App",
    description: "Ứng dụng dự báo thời tiết với giao diện đẹp và dữ liệu real-time",
    technologies: ["React", "JavaScript", "OpenWeather API", "CSS3"],
    features: [
      "Current weather data",
      "7-day forecast",
      "Location search",
      "Beautiful animations",
      "Dark/Light theme"
    ],
    githubUrl: "https://github.com/ducnguyen/weather-app",
    demoUrl: "https://weather-app-demo.vercel.app"
  }
];

export const experiences: Experience[] = [
  {
    id: "1",
    company: "Công ty ABC Technology",
    position: "Frontend Developer Intern",
    duration: "06/2024 - 12/2024",
    description: [
      "Phát triển giao diện người dùng cho ứng dụng web sử dụng React và TypeScript",
      "Tối ưu hóa hiệu suất website và cải thiện trải nghiệm người dùng",
      "Làm việc nhóm theo phương pháp Agile/Scrum",
      "Tham gia code review và học hỏi từ senior developers"
    ],
    technologies: ["React", "TypeScript", "Tailwind CSS", "Git", "Jira"]
  },
  {
    id: "2",
    company: "Freelance Projects",
    position: "Web Developer",
    duration: "01/2024 - 05/2024",
    description: [
      "Phát triển website cho các doanh nghiệp nhỏ",
      "Tạo landing page và portfolio websites",
      "Tối ưu SEO và hiệu suất website",
      "Hỗ trợ khách hàng và bảo trì website"
    ],
    technologies: ["HTML", "CSS", "JavaScript", "WordPress", "PHP"]
  }
];

export const education: Education[] = [
  {
    id: "1",
    school: "Đại học Công nghệ Thông tin",
    degree: "Cử nhân Công nghệ Thông tin",
    duration: "2020 - 2024",
    gpa: "3.2/4.0",
    description: "Chuyên ngành Công nghệ Phần mềm"
  },
  {
    id: "2",
    school: "FPT Software Academy",
    degree: "Frontend Development Certificate",
    duration: "2023",
    description: "Khóa học chuyên sâu về React và TypeScript"
  }
];
