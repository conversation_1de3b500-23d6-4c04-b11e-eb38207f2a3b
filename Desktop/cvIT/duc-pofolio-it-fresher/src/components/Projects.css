/* Projects Section Styles */
.projects {
  padding: 5rem 0;
  background: #f8fafc;
  position: relative;
}

.projects__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Section Header */
.projects__header {
  text-align: center;
  margin-bottom: 3rem;
}

.projects__title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  position: relative;
}

.projects__title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(45deg, #2563eb, #3b82f6);
  border-radius: 2px;
}

.projects__subtitle {
  font-size: 1.1rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Filter Buttons */
.projects__filters {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.projects__filter {
  padding: 0.75rem 1.5rem;
  border: 2px solid #e2e8f0;
  background: white;
  color: #64748b;
  border-radius: 50px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.projects__filter:hover {
  border-color: #2563eb;
  color: #2563eb;
  transform: translateY(-2px);
}

.projects__filter--active {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.projects__filter--active:hover {
  background: #1d4ed8;
  border-color: #1d4ed8;
}

/* Projects Grid */
.projects__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

/* Project Card */
.projects__card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
}

.projects__card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.projects__card--expanded {
  grid-column: 1 / -1;
  max-width: 800px;
  margin: 0 auto;
}

/* Project Image */
.projects__image {
  height: 200px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.projects__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.projects__card:hover .projects__image img {
  transform: scale(1.05);
}

.projects__image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

.projects__image-placeholder span {
  margin-top: 0.5rem;
  font-weight: 500;
}

/* Project Content */
.projects__content {
  padding: 2rem;
}

.projects__name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.75rem;
}

.projects__description {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

/* Technologies */
.projects__technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.projects__tech-tag {
  background: #f1f5f9;
  color: #475569;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid #e2e8f0;
}

/* Project Links */
.projects__links {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.projects__link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.projects__link--demo {
  background: #2563eb;
  color: white;
}

.projects__link--demo:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
}

.projects__link--github {
  background: #374151;
  color: white;
}

.projects__link--github:hover {
  background: #1f2937;
  transform: translateY(-2px);
}

/* Expand Button */
.projects__expand-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: #2563eb;
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem 0;
  transition: all 0.3s ease;
  width: 100%;
  justify-content: center;
  border-top: 1px solid #e2e8f0;
  margin-top: 1rem;
  padding-top: 1rem;
}

.projects__expand-btn:hover {
  color: #1d4ed8;
}

.projects__expand-icon--rotated {
  transform: rotate(180deg);
}

/* Expanded Content */
.projects__expanded {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.projects__features-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.projects__features {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.projects__feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #64748b;
  font-size: 0.9rem;
}

.projects__feature svg {
  color: #059669;
  flex-shrink: 0;
}

/* Empty State */
.projects__empty {
  text-align: center;
  padding: 4rem 2rem;
  color: #64748b;
}

.projects__empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.projects__empty h3 {
  font-size: 1.5rem;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.projects__empty p {
  margin-bottom: 2rem;
}

.projects__reset-filter {
  background: #2563eb;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 50px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.projects__reset-filter:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
}

/* Call to Action */
.projects__cta {
  text-align: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  border-radius: 16px;
  color: white;
}

.projects__cta-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.projects__cta-text {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.projects__cta-btn {
  background: white;
  color: #2563eb;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.projects__cta-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .projects {
    padding: 3rem 0;
  }

  .projects__container {
    padding: 0 1rem;
  }

  .projects__title {
    font-size: 2rem;
  }

  .projects__grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .projects__card--expanded {
    grid-column: 1;
  }

  .projects__filters {
    gap: 0.5rem;
  }

  .projects__filter {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .projects__links {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .projects__content {
    padding: 1.5rem;
  }

  .projects__cta {
    padding: 2rem 1rem;
  }

  .projects__cta-title {
    font-size: 1.25rem;
  }

  .projects__cta-text {
    font-size: 1rem;
  }
}
