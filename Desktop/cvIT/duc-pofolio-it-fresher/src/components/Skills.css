/* Skills Section Styles */
.skills {
  padding: 5rem 0;
  background: white;
  position: relative;
}

.skills__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Section Header */
.skills__header {
  text-align: center;
  margin-bottom: 4rem;
}

.skills__title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  position: relative;
}

.skills__title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(45deg, #2563eb, #3b82f6);
  border-radius: 2px;
}

.skills__subtitle {
  font-size: 1.1rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Skills Overview */
.skills__overview {
  margin-bottom: 4rem;
}

.skills__stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.skills__stat {
  text-align: center;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 16px;
  min-width: 150px;
  border: 1px solid #e2e8f0;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.skills__stat:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
}

.skills__stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2563eb;
  margin-bottom: 0.5rem;
}

.skills__stat-label {
  font-size: 1rem;
  color: #64748b;
  font-weight: 500;
}

/* Skills Content */
.skills__content {
  display: flex;
  flex-direction: column;
  gap: 3rem;
  margin-bottom: 4rem;
}

/* Category */
.skills__category {
  background: #f8fafc;
  border-radius: 20px;
  padding: 2.5rem;
  border: 1px solid #e2e8f0;
}

.skills__category-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.skills__category-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.skills__category-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  flex: 1;
}

.skills__category-count {
  background: #2563eb;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Skills Grid */
.skills__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

/* Skill Item */
.skills__item {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.skills__item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -3px rgba(0, 0, 0, 0.1);
}

.skills__item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.skills__item-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  flex: 1;
}

.skills__item-level {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.skills__level-badge {
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.skills__level-percent {
  font-size: 0.9rem;
  font-weight: 600;
  color: #64748b;
}

/* Progress Bar */
.skills__progress {
  margin-bottom: 1rem;
}

.skills__progress-track {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.skills__progress-fill {
  height: 100%;
  border-radius: 4px;
  position: relative;
}

.skills__progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.skills__item-footer {
  display: flex;
  justify-content: center;
}

.skills__experience {
  font-size: 1.2rem;
}

/* Learning Section */
.skills__learning {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 3rem;
  border-radius: 20px;
  color: white;
  margin-bottom: 3rem;
}

.skills__learning-title {
  font-size: 1.8rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 2rem;
}

.skills__learning-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.skills__learning-item {
  display: flex;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.skills__learning-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.skills__learning-text h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.skills__learning-text p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
  font-size: 0.9rem;
}

/* Call to Action */
.skills__cta {
  text-align: center;
  padding: 3rem 2rem;
  background: #f8fafc;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
}

.skills__cta-text {
  font-size: 1.1rem;
  color: #64748b;
  margin-bottom: 1.5rem;
}

.skills__cta-btn {
  background: #2563eb;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.skills__cta-btn:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .skills {
    padding: 3rem 0;
  }

  .skills__container {
    padding: 0 1rem;
  }

  .skills__title {
    font-size: 2rem;
  }

  .skills__stats {
    gap: 1.5rem;
  }

  .skills__stat {
    padding: 1.5rem;
    min-width: 120px;
  }

  .skills__category {
    padding: 2rem;
  }

  .skills__grid {
    grid-template-columns: 1fr;
  }

  .skills__learning {
    padding: 2rem;
  }

  .skills__learning-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .skills__stats {
    flex-direction: column;
    align-items: center;
  }

  .skills__category-header {
    flex-direction: column;
    text-align: center;
  }

  .skills__item-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .skills__item-level {
    align-items: flex-start;
  }
}
