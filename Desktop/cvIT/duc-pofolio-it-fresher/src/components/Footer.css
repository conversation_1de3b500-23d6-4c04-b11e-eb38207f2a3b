/* Footer Styles */
.footer {
  background: #0f172a;
  color: #e2e8f0;
  position: relative;
}

.footer__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Main Footer Content */
.footer__content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 3rem;
  padding: 4rem 0 2rem 0;
}

/* Footer Sections */
.footer__section {
  display: flex;
  flex-direction: column;
}

.footer__section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 1.5rem;
  position: relative;
}

.footer__section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 30px;
  height: 2px;
  background: #2563eb;
}

/* About Section */
.footer__section--about {
  max-width: 350px;
}

.footer__logo {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 700;
}

.footer__logo-text {
  color: #f1f5f9;
}

.footer__logo-dot {
  color: #2563eb;
  margin-left: 2px;
}

.footer__description {
  color: #94a3b8;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
}

.footer__contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.footer__contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #94a3b8;
  font-size: 0.9rem;
}

.footer__contact-item svg {
  color: #2563eb;
  flex-shrink: 0;
}

.footer__contact-item a {
  color: #94a3b8;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer__contact-item a:hover {
  color: #2563eb;
}

/* Links Section */
.footer__links {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.footer__link-item {
  transition: transform 0.3s ease;
}

.footer__link-item:hover {
  transform: translateX(5px);
}

.footer__link {
  color: #94a3b8;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
  cursor: pointer;
}

.footer__link:hover {
  color: #2563eb;
}

/* Social Section */
.footer__social {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.footer__social-link {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #1e293b;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid #334155;
}

.footer__social-link:hover {
  background: #2563eb;
  color: white;
  transform: translateY(-2px);
  border-color: #2563eb;
}

/* Newsletter */
.footer__newsletter {
  background: #1e293b;
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid #334155;
}

.footer__newsletter-text {
  color: #94a3b8;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.footer__newsletter-btn {
  width: 100%;
  background: #2563eb;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.footer__newsletter-btn:hover {
  background: #1d4ed8;
  transform: translateY(-1px);
}

/* Footer Bottom */
.footer__bottom {
  border-top: 1px solid #334155;
  padding: 2rem 0;
}

.footer__bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.footer__copyright {
  color: #64748b;
  font-size: 0.9rem;
}

.footer__copyright p {
  margin: 0;
  margin-bottom: 0.25rem;
}

.footer__built-with {
  font-size: 0.8rem;
  color: #475569;
}

.footer__bottom-links {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.footer__bottom-link {
  color: #64748b;
  font-size: 0.9rem;
  cursor: pointer;
  transition: color 0.3s ease;
}

.footer__bottom-link:hover {
  color: #2563eb;
}

.footer__back-to-top {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: 1px solid #334155;
  color: #94a3b8;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.footer__back-to-top:hover {
  background: #1e293b;
  border-color: #2563eb;
  color: #2563eb;
}

/* Floating Back to Top Button */
.footer__floating-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 50px;
  height: 50px;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
  transition: all 0.3s ease;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
}

.footer__floating-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.footer__floating-top:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer__content {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }

  .footer__section--about {
    max-width: none;
  }
}

@media (max-width: 768px) {
  .footer__container {
    padding: 0 1rem;
  }

  .footer__content {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 3rem 0 2rem 0;
  }

  .footer__bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .footer__bottom-links {
    justify-content: center;
    gap: 1rem;
  }

  .footer__floating-top {
    bottom: 1rem;
    right: 1rem;
    width: 45px;
    height: 45px;
  }
}

@media (max-width: 480px) {
  .footer__content {
    padding: 2rem 0 1.5rem 0;
  }

  .footer__social {
    justify-content: center;
  }

  .footer__newsletter {
    padding: 1rem;
  }

  .footer__bottom-links {
    flex-direction: column;
    gap: 0.5rem;
  }

  .footer__copyright {
    text-align: center;
  }
}

/* Scroll-triggered animations */
@media (prefers-reduced-motion: no-preference) {
  .footer__floating-top {
    animation: fadeInUp 0.3s ease;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .footer {
    background: #020617;
  }

  .footer__social-link {
    background: #0f172a;
    border-color: #1e293b;
  }

  .footer__newsletter {
    background: #0f172a;
    border-color: #1e293b;
  }

  .footer__back-to-top {
    border-color: #1e293b;
  }

  .footer__back-to-top:hover {
    background: #0f172a;
  }
}
