/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.header--scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

/* Logo */
.header__logo a {
  text-decoration: none;
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2563eb;
}

.header__logo-text {
  color: #1e293b;
}

.header__logo-dot {
  color: #2563eb;
  margin-left: 2px;
}

/* Desktop Navigation */
.header__nav {
  display: flex;
}

.header__nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.header__nav-item {
  position: relative;
}

.header__nav-link {
  text-decoration: none;
  color: #64748b;
  font-weight: 500;
  padding: 0.5rem 0;
  transition: color 0.3s ease;
  position: relative;
}

.header__nav-link:hover {
  color: #2563eb;
}

.header__nav-link--active {
  color: #2563eb;
}

.header__nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: #2563eb;
  transition: width 0.3s ease;
}

.header__nav-link:hover::after,
.header__nav-link--active::after {
  width: 100%;
}

/* Mobile Menu Button */
.header__menu-btn {
  display: none;
  flex-direction: column;
  justify-content: center;
  width: 30px;
  height: 30px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 1001;
}

.header__menu-line {
  width: 100%;
  height: 2px;
  background: #1e293b;
  margin: 3px 0;
  transition: all 0.3s ease;
  transform-origin: center;
}

.header__menu-btn--open .header__menu-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.header__menu-btn--open .header__menu-line:nth-child(2) {
  opacity: 0;
}

.header__menu-btn--open .header__menu-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation */
.header__mobile-nav {
  position: fixed;
  top: 70px;
  right: -100%;
  width: 280px;
  height: calc(100vh - 70px);
  background: white;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  transition: right 0.3s ease;
  z-index: 999;
}

.header__mobile-nav--open {
  right: 0;
}

.header__mobile-nav-list {
  list-style: none;
  margin: 0;
  padding: 2rem 0;
}

.header__mobile-nav-item {
  border-bottom: 1px solid #f1f5f9;
}

.header__mobile-nav-link {
  display: block;
  text-decoration: none;
  color: #64748b;
  font-weight: 500;
  padding: 1rem 2rem;
  transition: all 0.3s ease;
}

.header__mobile-nav-link:hover,
.header__mobile-nav-link--active {
  color: #2563eb;
  background: #f8fafc;
}

/* Mobile Overlay */
.header__overlay {
  position: fixed;
  top: 70px;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
}

/* Responsive */
@media (max-width: 768px) {
  .header__container {
    padding: 0 1rem;
  }

  .header__nav {
    display: none;
  }

  .header__menu-btn {
    display: flex;
  }
}

@media (max-width: 480px) {
  .header__mobile-nav {
    width: 100%;
    right: -100%;
  }

  .header__mobile-nav--open {
    right: 0;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .header {
    background: rgba(15, 23, 42, 0.95);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .header--scrolled {
    background: rgba(15, 23, 42, 0.98);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
  }

  .header__logo-text {
    color: #f1f5f9;
  }

  .header__nav-link {
    color: #94a3b8;
  }

  .header__nav-link:hover,
  .header__nav-link--active {
    color: #60a5fa;
  }

  .header__nav-link::after {
    background: #60a5fa;
  }

  .header__menu-line {
    background: #f1f5f9;
  }

  .header__mobile-nav {
    background: #0f172a;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.3);
  }

  .header__mobile-nav-item {
    border-bottom-color: #334155;
  }

  .header__mobile-nav-link {
    color: #94a3b8;
  }

  .header__mobile-nav-link:hover,
  .header__mobile-nav-link--active {
    color: #60a5fa;
    background: #1e293b;
  }
}
