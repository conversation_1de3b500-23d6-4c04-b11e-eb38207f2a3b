import React, { useState, useEffect, useRef } from 'react';
import { skills, Skill } from '../data/portfolioData';
import './Skills.css';

const Skills: React.FC = () => {
  const [visibleSkills, setVisibleSkills] = useState<Set<string>>(new Set());
  const skillsRef = useRef<HTMLDivElement>(null);

  const categories = {
    frontend: { name: 'Frontend', icon: '🎨', color: '#3b82f6' },
    backend: { name: 'Backend', icon: '⚙️', color: '#059669' },
    database: { name: 'Database', icon: '🗄️', color: '#dc2626' },
    tools: { name: 'Tools', icon: '🛠️', color: '#7c3aed' },
    other: { name: 'Other', icon: '📚', color: '#ea580c' }
  };

  const groupedSkills = skills.reduce((acc, skill) => {
    if (!acc[skill.category]) {
      acc[skill.category] = [];
    }
    acc[skill.category].push(skill);
    return acc;
  }, {} as Record<string, Skill[]>);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const skillName = entry.target.getAttribute('data-skill');
            if (skillName) {
              setTimeout(() => {
                setVisibleSkills(prev => new Set([...prev, skillName]));
              }, Math.random() * 500);
            }
          }
        });
      },
      { threshold: 0.3 }
    );

    const skillElements = skillsRef.current?.querySelectorAll('.skills__item');
    skillElements?.forEach(el => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  const getSkillLevel = (level: number): string => {
    if (level >= 80) return 'Expert';
    if (level >= 60) return 'Advanced';
    if (level >= 40) return 'Intermediate';
    return 'Beginner';
  };

  const getSkillLevelColor = (level: number): string => {
    if (level >= 80) return '#059669';
    if (level >= 60) return '#3b82f6';
    if (level >= 40) return '#f59e0b';
    return '#ef4444';
  };

  return (
    <section id="skills" className="skills">
      <div className="skills__container">
        {/* Section Header */}
        <div className="skills__header">
          <h2 className="skills__title">Kỹ năng & Công nghệ</h2>
          <p className="skills__subtitle">
            Những công nghệ và kỹ năng tôi đã học được và áp dụng trong các dự án
          </p>
        </div>

        {/* Skills Overview */}
        <div className="skills__overview">
          <div className="skills__stats">
            <div className="skills__stat">
              <div className="skills__stat-number">{skills.length}</div>
              <div className="skills__stat-label">Kỹ năng</div>
            </div>
            <div className="skills__stat">
              <div className="skills__stat-number">{Object.keys(categories).length}</div>
              <div className="skills__stat-label">Lĩnh vực</div>
            </div>
            <div className="skills__stat">
              <div className="skills__stat-number">
                {Math.round(skills.reduce((sum, skill) => sum + skill.level, 0) / skills.length)}%
              </div>
              <div className="skills__stat-label">Trung bình</div>
            </div>
          </div>
        </div>

        {/* Skills by Category */}
        <div className="skills__content" ref={skillsRef}>
          {Object.entries(groupedSkills).map(([categoryKey, categorySkills]) => {
            const category = categories[categoryKey as keyof typeof categories];
            return (
              <div key={categoryKey} className="skills__category">
                <div className="skills__category-header">
                  <div className="skills__category-icon">{category.icon}</div>
                  <h3 className="skills__category-title">{category.name}</h3>
                  <div className="skills__category-count">
                    {categorySkills.length} kỹ năng
                  </div>
                </div>

                <div className="skills__grid">
                  {categorySkills.map((skill) => (
                    <div
                      key={skill.name}
                      className="skills__item"
                      data-skill={skill.name}
                    >
                      <div className="skills__item-header">
                        <h4 className="skills__item-name">{skill.name}</h4>
                        <div className="skills__item-level">
                          <span 
                            className="skills__level-badge"
                            style={{ backgroundColor: getSkillLevelColor(skill.level) }}
                          >
                            {getSkillLevel(skill.level)}
                          </span>
                          <span className="skills__level-percent">{skill.level}%</span>
                        </div>
                      </div>

                      <div className="skills__progress">
                        <div className="skills__progress-track">
                          <div
                            className="skills__progress-fill"
                            style={{
                              width: visibleSkills.has(skill.name) ? `${skill.level}%` : '0%',
                              backgroundColor: category.color,
                              transition: 'width 1.5s ease-out'
                            }}
                          />
                        </div>
                      </div>

                      <div className="skills__item-footer">
                        <div className="skills__experience">
                          {skill.level >= 80 && '⭐⭐⭐'}
                          {skill.level >= 60 && skill.level < 80 && '⭐⭐'}
                          {skill.level >= 40 && skill.level < 60 && '⭐'}
                          {skill.level < 40 && '📚'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>

        {/* Learning Path */}
        <div className="skills__learning">
          <h3 className="skills__learning-title">Đang học và phát triển</h3>
          <div className="skills__learning-content">
            <div className="skills__learning-item">
              <div className="skills__learning-icon">🎯</div>
              <div className="skills__learning-text">
                <h4>Next.js & Server-Side Rendering</h4>
                <p>Đang tìm hiểu về SSR và static generation để tối ưu hiệu suất</p>
              </div>
            </div>
            <div className="skills__learning-item">
              <div className="skills__learning-icon">🔧</div>
              <div className="skills__learning-text">
                <h4>Testing (Jest, React Testing Library)</h4>
                <p>Học cách viết unit test và integration test cho React apps</p>
              </div>
            </div>
            <div className="skills__learning-item">
              <div className="skills__learning-icon">☁️</div>
              <div className="skills__learning-text">
                <h4>AWS & Cloud Services</h4>
                <p>Tìm hiểu về deployment và quản lý ứng dụng trên cloud</p>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="skills__cta">
          <p className="skills__cta-text">
            Muốn xem những kỹ năng này được áp dụng trong thực tế?
          </p>
          <button 
            className="skills__cta-btn"
            onClick={() => {
              const element = document.querySelector('#projects');
              if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'start' });
              }
            }}
          >
            Xem dự án của tôi
          </button>
        </div>
      </div>
    </section>
  );
};

export default Skills;
