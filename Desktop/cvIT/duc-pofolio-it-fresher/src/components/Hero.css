/* Hero Section Styles */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
  padding-top: 70px;
}

.hero__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
  position: relative;
  z-index: 2;
}

.hero__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: calc(100vh - 140px);
}

/* Text Content */
.hero__text {
  color: white;
}

.hero__greeting {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 500;
}

.hero__greeting-wave {
  animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(20deg); }
  75% { transform: rotate(-10deg); }
}

.hero__name {
  font-size: 3.5rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  line-height: 1.1;
  background: linear-gradient(45deg, #fff, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero__title {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  min-height: 2rem;
}

.hero__title-text {
  color: #e2e8f0;
}

.hero__title-dynamic {
  color: #fbbf24;
  font-weight: 600;
  position: relative;
}

.hero__cursor {
  animation: blink 1s infinite;
  color: #fbbf24;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.hero__description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #cbd5e1;
  margin-bottom: 2rem;
  max-width: 500px;
}

/* Buttons */
.hero__buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.hero__btn {
  padding: 0.75rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  font-size: 1rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.hero__btn--primary {
  background: #fbbf24;
  color: #1e293b;
}

.hero__btn--primary:hover {
  background: #f59e0b;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(251, 191, 36, 0.3);
}

.hero__btn--secondary {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.hero__btn--secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* Social Links */
.hero__social {
  display: flex;
  gap: 1rem;
}

.hero__social-link {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.hero__social-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Avatar */
.hero__image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero__avatar {
  position: relative;
  width: 300px;
  height: 300px;
}

.hero__avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.hero__avatar-ring {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 2px solid transparent;
  border-radius: 50%;
  background: linear-gradient(45deg, #fbbf24, #f59e0b, #fbbf24);
  background-clip: border-box;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Scroll Indicator */
.hero__scroll {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
}

.hero__scroll-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.hero__scroll-btn:hover {
  color: white;
  transform: translateY(-2px);
}

.hero__scroll-text {
  font-size: 0.9rem;
  font-weight: 500;
}

.hero__scroll-arrow {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-5px); }
  60% { transform: translateY(-3px); }
}

/* Background Elements */
.hero__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;
}

.hero__bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  animation: float 6s ease-in-out infinite;
}

.hero__bg-circle--1 {
  width: 200px;
  height: 200px;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.hero__bg-circle--2 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 10%;
  animation-delay: 2s;
}

.hero__bg-circle--3 {
  width: 100px;
  height: 100px;
  top: 50%;
  right: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero__content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .hero__name {
    font-size: 2.5rem;
  }

  .hero__title {
    font-size: 1.2rem;
  }

  .hero__avatar {
    width: 200px;
    height: 200px;
  }

  .hero__buttons {
    justify-content: center;
  }

  .hero__social {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .hero__container {
    padding: 0 1rem;
  }

  .hero__name {
    font-size: 2rem;
  }

  .hero__title {
    font-size: 1.1rem;
  }

  .hero__description {
    font-size: 1rem;
  }

  .hero__btn {
    padding: 0.6rem 1.5rem;
    font-size: 0.9rem;
  }

  .hero__avatar {
    width: 150px;
    height: 150px;
  }
}
