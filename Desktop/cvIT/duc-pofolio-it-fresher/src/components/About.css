/* About Section Styles */
.about {
  padding: 5rem 0;
  background: #f8fafc;
  position: relative;
}

.about__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Section Header */
.about__header {
  text-align: center;
  margin-bottom: 4rem;
}

.about__title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  position: relative;
}

.about__title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(45deg, #2563eb, #3b82f6);
  border-radius: 2px;
}

.about__subtitle {
  font-size: 1.1rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Main Content */
.about__content {
  display: grid;
  gap: 4rem;
}

.about__main {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  align-items: start;
}

/* Info Section */
.about__info {
  background: white;
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.about__info-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1.5rem;
}

.about__description p {
  color: #475569;
  line-height: 1.7;
  margin-bottom: 1.2rem;
  font-size: 1rem;
}

.about__description strong {
  color: #2563eb;
  font-weight: 600;
}

.about__details {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e2e8f0;
}

.about__detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  gap: 0.5rem;
}

.about__detail-label {
  font-weight: 500;
  color: #374151;
  min-width: 120px;
}

.about__detail-value {
  color: #2563eb;
  text-decoration: none;
  font-weight: 500;
}

.about__detail-value:hover {
  text-decoration: underline;
}

/* Stats */
.about__stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.about__stat {
  background: white;
  padding: 2rem 1.5rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.about__stat:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
}

.about__stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #2563eb;
  margin-bottom: 0.5rem;
}

.about__stat-label {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

/* Highlights */
.about__highlights {
  background: white;
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.about__highlights-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 2rem;
  text-align: center;
}

.about__highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.about__highlight {
  text-align: center;
  padding: 1.5rem;
  border-radius: 12px;
  background: #f8fafc;
  transition: transform 0.3s ease;
}

.about__highlight:hover {
  transform: translateY(-2px);
}

.about__highlight-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.about__highlight-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.about__highlight-description {
  font-size: 0.9rem;
  color: #64748b;
  line-height: 1.5;
}

/* Education */
.about__education {
  background: white;
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.about__education-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 2rem;
}

.about__education-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.about__education-item {
  display: flex;
  gap: 1.5rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  border-left: 4px solid #2563eb;
}

.about__education-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.about__education-content {
  flex: 1;
}

.about__education-degree {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.about__education-school {
  font-weight: 500;
  color: #2563eb;
  margin-bottom: 0.25rem;
}

.about__education-duration {
  font-size: 0.9rem;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.about__education-gpa {
  font-size: 0.9rem;
  color: #059669;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.about__education-description {
  font-size: 0.9rem;
  color: #64748b;
  line-height: 1.5;
  margin: 0;
}

/* Call to Action */
.about__cta {
  text-align: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  border-radius: 16px;
  color: white;
}

.about__cta-text {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.about__cta-btn {
  background: white;
  color: #2563eb;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.about__cta-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .about {
    padding: 3rem 0;
  }

  .about__container {
    padding: 0 1rem;
  }

  .about__title {
    font-size: 2rem;
  }

  .about__main {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .about__stats {
    grid-template-columns: 1fr 1fr;
  }

  .about__highlights-grid {
    grid-template-columns: 1fr;
  }

  .about__info,
  .about__highlights,
  .about__education {
    padding: 2rem;
  }

  .about__education-item {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .about__stats {
    grid-template-columns: 1fr;
  }

  .about__stat {
    padding: 1.5rem 1rem;
  }

  .about__detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .about__detail-label {
    min-width: auto;
  }
}
