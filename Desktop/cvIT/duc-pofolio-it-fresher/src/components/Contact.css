/* Contact Section Styles */
.contact {
  padding: 5rem 0;
  background: #f8fafc;
  position: relative;
}

.contact__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Section Header */
.contact__header {
  text-align: center;
  margin-bottom: 4rem;
}

.contact__title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  position: relative;
}

.contact__title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(45deg, #2563eb, #3b82f6);
  border-radius: 2px;
}

.contact__subtitle {
  font-size: 1.1rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Main Content */
.contact__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 4rem;
}

/* Contact Info */
.contact__info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact__intro {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.contact__intro-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.contact__intro-text {
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

/* Contact Methods */
.contact__methods {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact__method {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  text-decoration: none;
  transition: all 0.3s ease;
}

.contact__method:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -3px rgba(0, 0, 0, 0.1);
  border-color: #2563eb;
}

.contact__method-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  border-radius: 12px;
  flex-shrink: 0;
}

.contact__method-content {
  flex: 1;
}

.contact__method-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.contact__method-value {
  color: #2563eb;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.contact__method-description {
  color: #64748b;
  font-size: 0.9rem;
}

/* Social Links */
.contact__social {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.contact__social-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.contact__social-links {
  display: flex;
  gap: 1rem;
}

.contact__social-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #64748b;
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 500;
}

.contact__social-link:hover {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
  transform: translateY(-2px);
}

/* Contact Form */
.contact__form-container {
  background: white;
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  height: fit-content;
}

.contact__form-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 2rem;
  text-align: center;
}

.contact__form-group {
  margin-bottom: 1.5rem;
}

.contact__form-label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.contact__form-input,
.contact__form-select,
.contact__form-textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  font-family: inherit;
}

.contact__form-input:focus,
.contact__form-select:focus,
.contact__form-textarea:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.contact__form-textarea {
  resize: vertical;
  min-height: 120px;
}

.contact__form-submit {
  width: 100%;
  background: #2563eb;
  color: white;
  border: none;
  padding: 0.875rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.contact__form-submit:hover:not(:disabled) {
  background: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
}

.contact__form-submit:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.contact__form-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Form Messages */
.contact__form-message {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.contact__form-message--success {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.contact__form-message--error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

/* Quick Actions */
.contact__quick-actions {
  text-align: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  border-radius: 16px;
  color: white;
}

.contact__quick-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
}

.contact__quick-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.contact__quick-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.contact__quick-btn--email {
  background: white;
  color: #2563eb;
}

.contact__quick-btn--email:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.contact__quick-btn--phone {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.contact__quick-btn--phone:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .contact {
    padding: 3rem 0;
  }

  .contact__container {
    padding: 0 1rem;
  }

  .contact__title {
    font-size: 2rem;
  }

  .contact__content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .contact__form-container {
    padding: 2rem;
  }

  .contact__social-links {
    flex-direction: column;
  }

  .contact__quick-buttons {
    flex-direction: column;
    align-items: center;
  }

  .contact__quick-btn {
    width: 200px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .contact__intro,
  .contact__social,
  .contact__form-container {
    padding: 1.5rem;
  }

  .contact__method {
    flex-direction: column;
    text-align: center;
  }

  .contact__method-icon {
    margin: 0 auto;
  }

  .contact__quick-actions {
    padding: 2rem 1rem;
  }

  .contact__quick-title {
    font-size: 1.25rem;
  }
}
