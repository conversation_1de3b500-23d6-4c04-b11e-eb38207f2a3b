import React from 'react';
import { experiences } from '../data/portfolioData';
import './Experience.css';

const Experience: React.FC = () => {
  return (
    <section id="experience" className="experience">
      <div className="experience__container">
        {/* Section Header */}
        <div className="experience__header">
          <h2 className="experience__title">Kinh nghiệm làm việc</h2>
          <p className="experience__subtitle">
            Hành trình phát triển kỹ năng và kinh nghiệm của tôi trong lĩnh vực IT
          </p>
        </div>

        {/* Timeline */}
        <div className="experience__timeline">
          {experiences.map((exp, index) => (
            <div key={exp.id} className="experience__item">
              {/* Timeline Dot */}
              <div className="experience__dot">
                <div className="experience__dot-inner"></div>
              </div>

              {/* Timeline Line */}
              {index < experiences.length - 1 && (
                <div className="experience__line"></div>
              )}

              {/* Experience Card */}
              <div className="experience__card">
                <div className="experience__card-header">
                  <div className="experience__info">
                    <h3 className="experience__position">{exp.position}</h3>
                    <div className="experience__company">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                        <polyline points="9,22 9,12 15,12 15,22"/>
                      </svg>
                      {exp.company}
                    </div>
                    <div className="experience__duration">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <circle cx="12" cy="12" r="10"/>
                        <polyline points="12,6 12,12 16,14"/>
                      </svg>
                      {exp.duration}
                    </div>
                  </div>
                  
                  <div className="experience__status">
                    {index === 0 ? (
                      <span className="experience__status-badge experience__status-badge--current">
                        Hiện tại
                      </span>
                    ) : (
                      <span className="experience__status-badge experience__status-badge--completed">
                        Hoàn thành
                      </span>
                    )}
                  </div>
                </div>

                {/* Description */}
                <div className="experience__description">
                  <ul className="experience__tasks">
                    {exp.description.map((task, taskIndex) => (
                      <li key={taskIndex} className="experience__task">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <polyline points="20,6 9,17 4,12"/>
                        </svg>
                        {task}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Technologies */}
                <div className="experience__technologies">
                  <h4 className="experience__tech-title">Công nghệ sử dụng:</h4>
                  <div className="experience__tech-list">
                    {exp.technologies.map((tech, techIndex) => (
                      <span key={techIndex} className="experience__tech-tag">
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Achievement Badge */}
                <div className="experience__achievement">
                  {index === 0 && (
                    <div className="experience__badge experience__badge--current">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                      </svg>
                      Vị trí hiện tại
                    </div>
                  )}
                  {exp.company.includes('Freelance') && (
                    <div className="experience__badge experience__badge--freelance">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                      </svg>
                      Freelancer
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Skills Gained */}
        <div className="experience__skills">
          <h3 className="experience__skills-title">Kỹ năng đã phát triển</h3>
          <div className="experience__skills-grid">
            <div className="experience__skill-category">
              <div className="experience__skill-icon">💻</div>
              <h4>Technical Skills</h4>
              <ul>
                <li>Frontend Development với React & TypeScript</li>
                <li>Responsive Web Design</li>
                <li>Version Control với Git</li>
                <li>API Integration</li>
              </ul>
            </div>
            
            <div className="experience__skill-category">
              <div className="experience__skill-icon">🤝</div>
              <h4>Soft Skills</h4>
              <ul>
                <li>Làm việc nhóm hiệu quả</li>
                <li>Giao tiếp với khách hàng</li>
                <li>Quản lý thời gian</li>
                <li>Giải quyết vấn đề</li>
              </ul>
            </div>
            
            <div className="experience__skill-category">
              <div className="experience__skill-icon">🚀</div>
              <h4>Methodology</h4>
              <ul>
                <li>Agile/Scrum Development</li>
                <li>Code Review Process</li>
                <li>Testing & Debugging</li>
                <li>Continuous Learning</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Career Goals */}
        <div className="experience__goals">
          <h3 className="experience__goals-title">Mục tiêu nghề nghiệp</h3>
          <div className="experience__goals-content">
            <div className="experience__goal">
              <div className="experience__goal-icon">🎯</div>
              <div className="experience__goal-text">
                <h4>Ngắn hạn (6-12 tháng)</h4>
                <p>Trở thành Frontend Developer chính thức tại một công ty công nghệ, 
                   nâng cao kỹ năng React và học thêm về testing.</p>
              </div>
            </div>
            
            <div className="experience__goal">
              <div className="experience__goal-icon">🌟</div>
              <div className="experience__goal-text">
                <h4>Trung hạn (1-2 năm)</h4>
                <p>Phát triển kỹ năng fullstack, học backend technologies và 
                   có thể lead các dự án nhỏ.</p>
              </div>
            </div>
            
            <div className="experience__goal">
              <div className="experience__goal-icon">🚀</div>
              <div className="experience__goal-text">
                <h4>Dài hạn (3-5 năm)</h4>
                <p>Trở thành Senior Developer, mentor cho junior developers và 
                   đóng góp vào các dự án có tác động lớn.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="experience__cta">
          <h3 className="experience__cta-title">Sẵn sàng cho thử thách mới!</h3>
          <p className="experience__cta-text">
            Tôi đang tìm kiếm cơ hội để áp dụng kinh nghiệm đã có và học hỏi thêm 
            trong một môi trường làm việc chuyên nghiệp.
          </p>
          <div className="experience__cta-buttons">
            <button 
              className="experience__cta-btn experience__cta-btn--primary"
              onClick={() => {
                const element = document.querySelector('#contact');
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
              }}
            >
              Liên hệ tuyển dụng
            </button>
            <button 
              className="experience__cta-btn experience__cta-btn--secondary"
              onClick={() => {
                const element = document.querySelector('#projects');
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
              }}
            >
              Xem portfolio
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Experience;
