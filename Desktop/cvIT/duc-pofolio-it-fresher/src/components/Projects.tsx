import React, { useState } from 'react';
import { projects } from '../data/portfolioData';
import './Projects.css';

const Projects: React.FC = () => {
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [filter, setFilter] = useState<string>('all');

  // Get unique technologies for filter
  const allTechnologies = Array.from(
    new Set(projects.flatMap(project => project.technologies))
  );

  const filteredProjects = filter === 'all' 
    ? projects 
    : projects.filter(project => 
        project.technologies.some(tech => 
          tech.toLowerCase().includes(filter.toLowerCase())
        )
      );

  const handleProjectClick = (projectId: string) => {
    setSelectedProject(selectedProject === projectId ? null : projectId);
  };

  return (
    <section id="projects" className="projects">
      <div className="projects__container">
        {/* Section Header */}
        <div className="projects__header">
          <h2 className="projects__title">Dự án của tôi</h2>
          <p className="projects__subtitle">
            Những dự án tôi đã thực hiện trong quá trình học tập và phát triển kỹ năng
          </p>
        </div>

        {/* Filter Buttons */}
        <div className="projects__filters">
          <button
            className={`projects__filter ${filter === 'all' ? 'projects__filter--active' : ''}`}
            onClick={() => setFilter('all')}
          >
            Tất cả ({projects.length})
          </button>
          {['React', 'TypeScript', 'Node.js', 'JavaScript'].map((tech) => {
            const count = projects.filter(p => 
              p.technologies.some(t => t.toLowerCase().includes(tech.toLowerCase()))
            ).length;
            
            if (count === 0) return null;
            
            return (
              <button
                key={tech}
                className={`projects__filter ${filter === tech ? 'projects__filter--active' : ''}`}
                onClick={() => setFilter(tech)}
              >
                {tech} ({count})
              </button>
            );
          })}
        </div>

        {/* Projects Grid */}
        <div className="projects__grid">
          {filteredProjects.map((project) => (
            <div
              key={project.id}
              className={`projects__card ${
                selectedProject === project.id ? 'projects__card--expanded' : ''
              }`}
            >
              {/* Project Image/Placeholder */}
              <div className="projects__image">
                {project.image ? (
                  <img src={project.image} alt={project.title} />
                ) : (
                  <div className="projects__image-placeholder">
                    <svg width="80" height="80" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    <span>Demo Project</span>
                  </div>
                )}
              </div>

              {/* Project Content */}
              <div className="projects__content">
                <h3 className="projects__name">{project.title}</h3>
                <p className="projects__description">{project.description}</p>

                {/* Technologies */}
                <div className="projects__technologies">
                  {project.technologies.map((tech, index) => (
                    <span key={index} className="projects__tech-tag">
                      {tech}
                    </span>
                  ))}
                </div>

                {/* Project Links */}
                <div className="projects__links">
                  {project.demoUrl && (
                    <a
                      href={project.demoUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="projects__link projects__link--demo"
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/>
                        <polyline points="15,3 21,3 21,9"/>
                        <line x1="10" y1="14" x2="21" y2="3"/>
                      </svg>
                      Live Demo
                    </a>
                  )}
                  {project.githubUrl && (
                    <a
                      href={project.githubUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="projects__link projects__link--github"
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                      </svg>
                      GitHub
                    </a>
                  )}
                </div>

                {/* Expand Button */}
                <button
                  className="projects__expand-btn"
                  onClick={() => handleProjectClick(project.id)}
                >
                  {selectedProject === project.id ? 'Thu gọn' : 'Xem chi tiết'}
                  <svg 
                    width="16" 
                    height="16" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    stroke="currentColor" 
                    strokeWidth="2"
                    className={selectedProject === project.id ? 'projects__expand-icon--rotated' : ''}
                  >
                    <polyline points="6,9 12,15 18,9"/>
                  </svg>
                </button>

                {/* Expanded Content */}
                {selectedProject === project.id && (
                  <div className="projects__expanded">
                    <h4 className="projects__features-title">Tính năng chính:</h4>
                    <ul className="projects__features">
                      {project.features.map((feature, index) => (
                        <li key={index} className="projects__feature">
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <polyline points="20,6 9,17 4,12"/>
                          </svg>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* No Projects Message */}
        {filteredProjects.length === 0 && (
          <div className="projects__empty">
            <div className="projects__empty-icon">📂</div>
            <h3>Không tìm thấy dự án</h3>
            <p>Không có dự án nào phù hợp với bộ lọc "{filter}"</p>
            <button 
              className="projects__reset-filter"
              onClick={() => setFilter('all')}
            >
              Xem tất cả dự án
            </button>
          </div>
        )}

        {/* Call to Action */}
        <div className="projects__cta">
          <h3 className="projects__cta-title">Có ý tưởng dự án thú vị?</h3>
          <p className="projects__cta-text">
            Tôi luôn sẵn sàng tham gia vào những dự án mới và thú vị. 
            Hãy liên hệ để thảo luận!
          </p>
          <button 
            className="projects__cta-btn"
            onClick={() => {
              const element = document.querySelector('#contact');
              if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'start' });
              }
            }}
          >
            Liên hệ với tôi
          </button>
        </div>
      </div>
    </section>
  );
};

export default Projects;
