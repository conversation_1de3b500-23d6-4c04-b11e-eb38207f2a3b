/* Experience Section Styles */
.experience {
  padding: 5rem 0;
  background: white;
  position: relative;
}

.experience__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Section Header */
.experience__header {
  text-align: center;
  margin-bottom: 4rem;
}

.experience__title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  position: relative;
}

.experience__title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(45deg, #2563eb, #3b82f6);
  border-radius: 2px;
}

.experience__subtitle {
  font-size: 1.1rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Timeline */
.experience__timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto 4rem auto;
}

.experience__item {
  position: relative;
  margin-bottom: 3rem;
  padding-left: 3rem;
}

.experience__item:last-child {
  margin-bottom: 0;
}

/* Timeline Dot */
.experience__dot {
  position: absolute;
  left: 0;
  top: 1rem;
  width: 20px;
  height: 20px;
  background: white;
  border: 4px solid #2563eb;
  border-radius: 50%;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.experience__dot-inner {
  width: 8px;
  height: 8px;
  background: #2563eb;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Timeline Line */
.experience__line {
  position: absolute;
  left: 9px;
  top: 2rem;
  width: 2px;
  height: calc(100% + 1rem);
  background: linear-gradient(to bottom, #2563eb, #e2e8f0);
  z-index: 1;
}

/* Experience Card */
.experience__card {
  background: #f8fafc;
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
}

.experience__card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
  background: white;
}

.experience__card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.experience__info {
  flex: 1;
}

.experience__position {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.experience__company {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #2563eb;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.experience__duration {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.9rem;
}

/* Status Badge */
.experience__status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.experience__status-badge--current {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.experience__status-badge--completed {
  background: #e0e7ff;
  color: #3730a3;
  border: 1px solid #c7d2fe;
}

/* Description */
.experience__description {
  margin-bottom: 1.5rem;
}

.experience__tasks {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.experience__task {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  color: #475569;
  line-height: 1.6;
}

.experience__task svg {
  color: #059669;
  flex-shrink: 0;
  margin-top: 0.1rem;
}

/* Technologies */
.experience__technologies {
  margin-bottom: 1rem;
}

.experience__tech-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.75rem;
}

.experience__tech-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.experience__tech-tag {
  background: #e0e7ff;
  color: #3730a3;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid #c7d2fe;
}

/* Achievement Badge */
.experience__achievement {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.experience__badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.experience__badge--current {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.experience__badge--freelance {
  background: #f3e8ff;
  color: #6b21a8;
  border: 1px solid #e9d5ff;
}

/* Skills Section */
.experience__skills {
  margin-bottom: 4rem;
}

.experience__skills-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #1e293b;
  text-align: center;
  margin-bottom: 2rem;
}

.experience__skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.experience__skill-category {
  background: #f8fafc;
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  text-align: center;
  transition: all 0.3s ease;
}

.experience__skill-category:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
  background: white;
}

.experience__skill-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.experience__skill-category h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.experience__skill-category ul {
  list-style: none;
  text-align: left;
}

.experience__skill-category li {
  color: #64748b;
  margin-bottom: 0.5rem;
  padding-left: 1rem;
  position: relative;
}

.experience__skill-category li::before {
  content: '•';
  color: #2563eb;
  position: absolute;
  left: 0;
  font-weight: bold;
}

/* Career Goals */
.experience__goals {
  margin-bottom: 4rem;
}

.experience__goals-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #1e293b;
  text-align: center;
  margin-bottom: 2rem;
}

.experience__goals-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.experience__goal {
  display: flex;
  gap: 1.5rem;
  background: #f8fafc;
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.experience__goal:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -3px rgba(0, 0, 0, 0.1);
  background: white;
}

.experience__goal-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
}

.experience__goal-text h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.experience__goal-text p {
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

/* Call to Action */
.experience__cta {
  text-align: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;
}

.experience__cta-title {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.experience__cta-text {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.experience__cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.experience__cta-btn {
  padding: 0.75rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.experience__cta-btn--primary {
  background: white;
  color: #2563eb;
}

.experience__cta-btn--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.experience__cta-btn--secondary {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.experience__cta-btn--secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .experience {
    padding: 3rem 0;
  }

  .experience__container {
    padding: 0 1rem;
  }

  .experience__title {
    font-size: 2rem;
  }

  .experience__item {
    padding-left: 2rem;
  }

  .experience__card {
    padding: 1.5rem;
  }

  .experience__card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .experience__skills-grid {
    grid-template-columns: 1fr;
  }

  .experience__goal {
    flex-direction: column;
    text-align: center;
  }

  .experience__cta-buttons {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .experience__dot {
    left: -5px;
  }

  .experience__line {
    left: 4px;
  }

  .experience__item {
    padding-left: 1.5rem;
  }

  .experience__cta {
    padding: 2rem 1rem;
  }

  .experience__cta-title {
    font-size: 1.5rem;
  }
}
