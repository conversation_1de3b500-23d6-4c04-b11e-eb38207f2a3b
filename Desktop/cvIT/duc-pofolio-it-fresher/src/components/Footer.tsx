import React from 'react';
import { personalInfo } from '../data/portfolioData';
import './Footer.css';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { name: '<PERSON><PERSON> chủ', href: '#home' },
    { name: '<PERSON><PERSON><PERSON><PERSON> thiệ<PERSON>', href: '#about' },
    { name: '<PERSON><PERSON> n<PERSON>', href: '#skills' },
    { name: 'D<PERSON> án', href: '#projects' },
    { name: '<PERSON><PERSON>', href: '#experience' },
    { name: '<PERSON><PERSON><PERSON> h<PERSON>', href: '#contact' }
  ];

  const socialLinks = [
    {
      name: 'GitH<PERSON>',
      url: personalInfo.github,
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
        </svg>
      )
    },
    {
      name: 'LinkedIn',
      url: personalInfo.linkedin,
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
        </svg>
      )
    }
  ];

  if (personalInfo.facebook) {
    socialLinks.push({
      name: 'Facebook',
      url: personalInfo.facebook,
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
        </svg>
      )
    });
  }

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  return (
    <footer className="footer">
      <div className="footer__container">
        {/* Main Footer Content */}
        <div className="footer__content">
          {/* About Section */}
          <div className="footer__section footer__section--about">
            <div className="footer__logo">
              <span className="footer__logo-text">{personalInfo.name}</span>
              <span className="footer__logo-dot">.</span>
            </div>
            <p className="footer__description">
              IT Fresher đam mê phát triển web với React và TypeScript. 
              Luôn sẵn sàng học hỏi và đóng góp vào các dự án thú vị.
            </p>
            <div className="footer__contact-info">
              <div className="footer__contact-item">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                  <polyline points="22,6 12,13 2,6"/>
                </svg>
                <a href={`mailto:${personalInfo.email}`}>{personalInfo.email}</a>
              </div>
              <div className="footer__contact-item">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                  <circle cx="12" cy="10" r="3"/>
                </svg>
                <span>{personalInfo.location}</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className="footer__section footer__section--links">
            <h3 className="footer__section-title">Liên kết nhanh</h3>
            <ul className="footer__links">
              {quickLinks.map((link, index) => (
                <li key={index} className="footer__link-item">
                  <a
                    href={link.href}
                    className="footer__link"
                    onClick={(e) => {
                      e.preventDefault();
                      scrollToSection(link.href);
                    }}
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div className="footer__section footer__section--services">
            <h3 className="footer__section-title">Dịch vụ</h3>
            <ul className="footer__links">
              <li className="footer__link-item">
                <span className="footer__link">Frontend Development</span>
              </li>
              <li className="footer__link-item">
                <span className="footer__link">React Applications</span>
              </li>
              <li className="footer__link-item">
                <span className="footer__link">Responsive Design</span>
              </li>
              <li className="footer__link-item">
                <span className="footer__link">Website Optimization</span>
              </li>
              <li className="footer__link-item">
                <span className="footer__link">Code Review</span>
              </li>
            </ul>
          </div>

          {/* Social & Newsletter */}
          <div className="footer__section footer__section--social">
            <h3 className="footer__section-title">Kết nối với tôi</h3>
            <div className="footer__social">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="footer__social-link"
                  aria-label={social.name}
                >
                  {social.icon}
                </a>
              ))}
            </div>
            
            <div className="footer__newsletter">
              <p className="footer__newsletter-text">
                Muốn cập nhật về các dự án mới của tôi?
              </p>
              <button 
                className="footer__newsletter-btn"
                onClick={() => scrollToSection('#contact')}
              >
                Liên hệ ngay
              </button>
            </div>
          </div>
        </div>

        {/* Footer Bottom */}
        <div className="footer__bottom">
          <div className="footer__bottom-content">
            <div className="footer__copyright">
              <p>
                © {currentYear} {personalInfo.name}. Tất cả quyền được bảo lưu.
              </p>
              <p className="footer__built-with">
                Được xây dựng với ❤️ bằng React & TypeScript
              </p>
            </div>
            
            <div className="footer__bottom-links">
              <span className="footer__bottom-link">Privacy Policy</span>
              <span className="footer__bottom-link">Terms of Service</span>
              <button 
                className="footer__back-to-top"
                onClick={scrollToTop}
                aria-label="Back to top"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M18 15l-6-6-6 6"/>
                </svg>
                Lên đầu trang
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Back to Top Button */}
      <button 
        className="footer__floating-top"
        onClick={scrollToTop}
        aria-label="Back to top"
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M18 15l-6-6-6 6"/>
        </svg>
      </button>
    </footer>
  );
};

export default Footer;
