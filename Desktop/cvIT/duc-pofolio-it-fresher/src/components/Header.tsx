import React, { useState, useEffect } from 'react';
import './Header.css';

interface HeaderProps {
  activeSection?: string;
}

const Header: React.FC<HeaderProps> = ({ activeSection = 'home' }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  const menuItems = [
    { id: 'home', label: 'Trang chủ', href: '#home' },
    { id: 'about', label: 'Giới thiệu', href: '#about' },
    { id: 'skills', label: '<PERSON><PERSON> năng', href: '#skills' },
    { id: 'projects', label: 'Dự án', href: '#projects' },
    { id: 'experience', label: 'Kinh nghiệm', href: '#experience' },
    { id: 'contact', label: 'Liên hệ', href: '#contact' }
  ];

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleMenuClick = (href: string) => {
    setIsMenuOpen(false);
    
    // Smooth scroll to section
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  return (
    <header className={`header ${isScrolled ? 'header--scrolled' : ''}`}>
      <div className="header__container">
        {/* Logo */}
        <div className="header__logo">
          <a href="#home" onClick={(e) => {
            e.preventDefault();
            handleMenuClick('#home');
          }}>
            <span className="header__logo-text">Đức</span>
            <span className="header__logo-dot">.</span>
          </a>
        </div>

        {/* Desktop Navigation */}
        <nav className="header__nav">
          <ul className="header__nav-list">
            {menuItems.map((item) => (
              <li key={item.id} className="header__nav-item">
                <a
                  href={item.href}
                  className={`header__nav-link ${
                    activeSection === item.id ? 'header__nav-link--active' : ''
                  }`}
                  onClick={(e) => {
                    e.preventDefault();
                    handleMenuClick(item.href);
                  }}
                >
                  {item.label}
                </a>
              </li>
            ))}
          </ul>
        </nav>

        {/* Mobile Menu Button */}
        <button
          className={`header__menu-btn ${isMenuOpen ? 'header__menu-btn--open' : ''}`}
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          aria-label="Toggle menu"
        >
          <span className="header__menu-line"></span>
          <span className="header__menu-line"></span>
          <span className="header__menu-line"></span>
        </button>

        {/* Mobile Navigation */}
        <nav className={`header__mobile-nav ${isMenuOpen ? 'header__mobile-nav--open' : ''}`}>
          <ul className="header__mobile-nav-list">
            {menuItems.map((item) => (
              <li key={item.id} className="header__mobile-nav-item">
                <a
                  href={item.href}
                  className={`header__mobile-nav-link ${
                    activeSection === item.id ? 'header__mobile-nav-link--active' : ''
                  }`}
                  onClick={(e) => {
                    e.preventDefault();
                    handleMenuClick(item.href);
                  }}
                >
                  {item.label}
                </a>
              </li>
            ))}
          </ul>
        </nav>

        {/* Mobile Menu Overlay */}
        {isMenuOpen && (
          <div 
            className="header__overlay"
            onClick={() => setIsMenuOpen(false)}
          />
        )}
      </div>
    </header>
  );
};

export default Header;
