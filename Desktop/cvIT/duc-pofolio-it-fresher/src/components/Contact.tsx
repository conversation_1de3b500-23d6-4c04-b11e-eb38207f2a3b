import React, { useState } from 'react';
import { personalInfo } from '../data/portfolioData';
import './Contact.css';

interface FormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

const Contact: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSubmitStatus('success');
      setFormData({ name: '', email: '', subject: '', message: '' });
    } catch (error) {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setSubmitStatus('idle'), 5000);
    }
  };

  const contactMethods = [
    {
      icon: '📧',
      title: 'Email',
      value: personalInfo.email,
      link: `mailto:${personalInfo.email}`,
      description: 'Gửi email trực tiếp cho tôi'
    },
    {
      icon: '📱',
      title: 'Điện thoại',
      value: personalInfo.phone,
      link: `tel:${personalInfo.phone}`,
      description: 'Gọi điện trực tiếp'
    },
    {
      icon: '📍',
      title: 'Địa chỉ',
      value: personalInfo.location,
      link: '#',
      description: 'Vị trí hiện tại của tôi'
    }
  ];

  const socialLinks = [
    {
      name: 'GitHub',
      url: personalInfo.github,
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
        </svg>
      )
    },
    {
      name: 'LinkedIn',
      url: personalInfo.linkedin,
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
        </svg>
      )
    }
  ];

  if (personalInfo.facebook) {
    socialLinks.push({
      name: 'Facebook',
      url: personalInfo.facebook,
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
        </svg>
      )
    });
  }

  return (
    <section id="contact" className="contact">
      <div className="contact__container">
        {/* Section Header */}
        <div className="contact__header">
          <h2 className="contact__title">Liên hệ với tôi</h2>
          <p className="contact__subtitle">
            Sẵn sàng thảo luận về cơ hội hợp tác hoặc dự án mới. Hãy liên hệ!
          </p>
        </div>

        <div className="contact__content">
          {/* Contact Info */}
          <div className="contact__info">
            <div className="contact__intro">
              <h3 className="contact__intro-title">Hãy kết nối với tôi!</h3>
              <p className="contact__intro-text">
                Tôi luôn sẵn sàng lắng nghe những ý tưởng mới và cơ hội hợp tác thú vị. 
                Dù bạn có một dự án cần thực hiện hay chỉ muốn trò chuyện về công nghệ, 
                đừng ngần ngại liên hệ với tôi.
              </p>
            </div>

            {/* Contact Methods */}
            <div className="contact__methods">
              {contactMethods.map((method, index) => (
                <a
                  key={index}
                  href={method.link}
                  className="contact__method"
                  target={method.link.startsWith('http') ? '_blank' : '_self'}
                  rel={method.link.startsWith('http') ? 'noopener noreferrer' : ''}
                >
                  <div className="contact__method-icon">{method.icon}</div>
                  <div className="contact__method-content">
                    <h4 className="contact__method-title">{method.title}</h4>
                    <p className="contact__method-value">{method.value}</p>
                    <span className="contact__method-description">{method.description}</span>
                  </div>
                </a>
              ))}
            </div>

            {/* Social Links */}
            <div className="contact__social">
              <h4 className="contact__social-title">Theo dõi tôi trên:</h4>
              <div className="contact__social-links">
                {socialLinks.map((social, index) => (
                  <a
                    key={index}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="contact__social-link"
                    aria-label={social.name}
                  >
                    {social.icon}
                    <span>{social.name}</span>
                  </a>
                ))}
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="contact__form-container">
            <form className="contact__form" onSubmit={handleSubmit}>
              <h3 className="contact__form-title">Gửi tin nhắn</h3>
              
              <div className="contact__form-group">
                <label htmlFor="name" className="contact__form-label">
                  Họ và tên *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="contact__form-input"
                  required
                  placeholder="Nhập họ và tên của bạn"
                />
              </div>

              <div className="contact__form-group">
                <label htmlFor="email" className="contact__form-label">
                  Email *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="contact__form-input"
                  required
                  placeholder="<EMAIL>"
                />
              </div>

              <div className="contact__form-group">
                <label htmlFor="subject" className="contact__form-label">
                  Chủ đề *
                </label>
                <select
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  className="contact__form-select"
                  required
                >
                  <option value="">Chọn chủ đề</option>
                  <option value="job">Cơ hội việc làm</option>
                  <option value="project">Hợp tác dự án</option>
                  <option value="freelance">Freelance</option>
                  <option value="consultation">Tư vấn</option>
                  <option value="other">Khác</option>
                </select>
              </div>

              <div className="contact__form-group">
                <label htmlFor="message" className="contact__form-label">
                  Tin nhắn *
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  className="contact__form-textarea"
                  rows={6}
                  required
                  placeholder="Hãy chia sẻ chi tiết về dự án hoặc cơ hội bạn muốn thảo luận..."
                />
              </div>

              <button
                type="submit"
                className="contact__form-submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <svg className="contact__form-spinner" width="20" height="20" viewBox="0 0 24 24">
                      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" opacity="0.3"/>
                      <path d="M12 2 A10 10 0 0 1 22 12" stroke="currentColor" strokeWidth="4" fill="none"/>
                    </svg>
                    Đang gửi...
                  </>
                ) : (
                  <>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M22 2L11 13"/>
                      <polygon points="22,2 15,22 11,13 2,9"/>
                    </svg>
                    Gửi tin nhắn
                  </>
                )}
              </button>

              {/* Submit Status */}
              {submitStatus === 'success' && (
                <div className="contact__form-message contact__form-message--success">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <polyline points="20,6 9,17 4,12"/>
                  </svg>
                  Tin nhắn đã được gửi thành công! Tôi sẽ phản hồi sớm nhất có thể.
                </div>
              )}

              {submitStatus === 'error' && (
                <div className="contact__form-message contact__form-message--error">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="12" cy="12" r="10"/>
                    <line x1="15" y1="9" x2="9" y2="15"/>
                    <line x1="9" y1="9" x2="15" y2="15"/>
                  </svg>
                  Có lỗi xảy ra khi gửi tin nhắn. Vui lòng thử lại hoặc liên hệ trực tiếp qua email.
                </div>
              )}
            </form>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="contact__quick-actions">
          <h3 className="contact__quick-title">Hoặc liên hệ nhanh qua:</h3>
          <div className="contact__quick-buttons">
            <a
              href={`mailto:${personalInfo.email}?subject=Cơ hội hợp tác&body=Xin chào ${personalInfo.name},%0D%0A%0D%0ATôi muốn thảo luận về...`}
              className="contact__quick-btn contact__quick-btn--email"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                <polyline points="22,6 12,13 2,6"/>
              </svg>
              Gửi Email
            </a>
            <a
              href={`tel:${personalInfo.phone}`}
              className="contact__quick-btn contact__quick-btn--phone"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
              </svg>
              Gọi điện
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
